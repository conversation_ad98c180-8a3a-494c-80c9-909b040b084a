{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../app/*"], "@/*": ["../app/*"], "~~/*": ["../*"], "@@/*": ["../*"], "nitropack/types": ["../../node_modules/.pnpm/nitropack@2.11.12_better-sqlite3@11.10.0/node_modules/nitropack/types"], "nitropack/runtime": ["../../node_modules/.pnpm/nitropack@2.11.12_better-sqlite3@11.10.0/node_modules/nitropack/runtime"], "nitropack": ["../../node_modules/.pnpm/nitropack@2.11.12_better-sqlite3@11.10.0/node_modules/nitropack"], "defu": ["../../node_modules/.pnpm/defu@6.1.4/node_modules/defu"], "h3": ["../../node_modules/.pnpm/h3@1.15.3/node_modules/h3"], "consola": ["../../node_modules/.pnpm/consola@3.4.2/node_modules/consola"], "ofetch": ["../../node_modules/.pnpm/ofetch@1.4.1/node_modules/ofetch"], "@unhead/vue": ["../../node_modules/.pnpm/@unhead+vue@2.0.10_vue@3.5.16_typescript@5.8.3_/node_modules/@unhead/vue"], "@nuxt/devtools": ["../../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_4e61256f8ebebad5cf76052dbb58c995/node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../../node_modules/.pnpm/@vue+runtime-core@3.5.16/node_modules/@vue/runtime-core"], "vue-router": ["../../node_modules/.pnpm/vue-router@4.5.1_vue@3.5.16_typescript@5.8.3_/node_modules/vue-router"], "vue-router/auto-routes": ["../../node_modules/.pnpm/vue-router@4.5.1_vue@3.5.16_typescript@5.8.3_/node_modules/vue-router/vue-router-auto-routes"], "unplugin-vue-router/client": ["../../node_modules/.pnpm/unplugin-vue-router@0.12.0__be20ca59a360e203deee0ecabe1c1bd4/node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../../node_modules/.pnpm/@nuxt+schema@3.16.2/node_modules/@nuxt/schema"], "nuxt": ["../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt"], "vite/client": ["../../node_modules/.pnpm/vite@6.3.5_@types+node@22.1_2cd2f67066f8f04fb968fdcd96babf83/node_modules/vite/client"], "#shared": ["../shared"], "assets": ["../app/assets"], "assets/*": ["../app/assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/core/runtime/nitro/utils/paths"], "#layers/@cssninja/tairo": ["../../layers/tairo"], "#layers/@cssninja/tairo/*": ["../../layers/tairo/*"], "#nuxt-component-meta": ["./component-meta"], "#nuxt-component-meta/types": ["./component-meta.d"], "#image": ["../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime"], "#image/*": ["../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/*"], "#content/components": ["./content/components"], "#content/manifest": ["./content/manifest"], "#mdc-configs": ["./mdc-configs"], "#mdc-highlighter": ["./mdc-highlighter"], "#mdc-imports": ["./mdc-imports"], "#pwa": ["../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/dist/runtime/composables/index"], "#color-mode-options": ["./color-mode-options.mjs"], "#unhead/composables": ["../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/composables/v4"], "vue-i18n": ["../../node_modules/.pnpm/vue-i18n@10.0.7_vue@3.5.16_typescript@5.8.3_/node_modules/vue-i18n/dist/vue-i18n"], "@intlify/shared": ["../../node_modules/.pnpm/@intlify+shared@10.0.7/node_modules/@intlify/shared/dist/shared"], "@intlify/message-compiler": ["../../node_modules/.pnpm/@intlify+message-compiler@11.1.5/node_modules/@intlify/message-compiler/dist/message-compiler"], "@intlify/core-base": ["../../node_modules/.pnpm/@intlify+core-base@10.0.7/node_modules/@intlify/core-base/dist/core-base"], "@intlify/core": ["../../node_modules/.pnpm/@intlify+core@10.0.7/node_modules/@intlify/core/dist/core.node"], "@intlify/utils/h3": ["../../node_modules/.pnpm/@intlify+utils@0.13.0/node_modules/@intlify/utils/dist/h3"], "ufo": ["../../node_modules/.pnpm/ufo@1.6.1/node_modules/ufo/dist/index"], "#i18n": ["../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/composables/index"], "#internal-i18n-types": ["../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/types"], "#nuxt-i18n/logger": ["./nuxt-i18n-logger"], "#nuxt-icon-server-bundle": ["./nuxt-icon-server-bundle"], "#content/dump": ["./content/database.compressed"], "#content/adapter": ["./db0/connectors/better-sqlite3"], "#content/local-adapter": ["./db0/connectors/better-sqlite3"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./content/types.d.ts", "./types/nitro-nuxt.d.ts", "../modules/content-documentation.ts/runtime/server", "../modules/purge-comments.ts/runtime/server", "../../../../../ti/runtime/server", "../../node_modules/.pnpm/@vueuse+nuxt@13.3.0_magicas_c83dd1e26dc7fa31b8d2951b71b982cc/node_modules/@vueuse/nuxt/runtime/server", "../../node_modules/.pnpm/nuxt-component-meta@0.10.1_magicast@0.3.5/node_modules/nuxt-component-meta/runtime/server", "../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/runtime/server", "../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/runtime/server", "../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/runtime/server", "../../node_modules/.pnpm/@nuxt+content@3.4.0_magicast@0.3.5_typescript@5.8.3/node_modules/@nuxt/content/runtime/server", "../../node_modules/.pnpm/@nuxt+fonts@0.11.4_db0@0.3._96a303972b0a0111acac0c3036fb3956/node_modules/@nuxt/fonts/runtime/server", "../../node_modules/.pnpm/@pinia+nuxt@0.11.0_magicast_5c79cbd05b599f0ffb2fb3def69cce33/node_modules/@pinia/nuxt/runtime/server", "../../runtime/server", "../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/runtime/server", "../../../../AppData/Roaming/npm/node_modules/@nuxt/devtools/module.cjs/runtime/server", "../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/runtime/server", "../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/runtime/server", "../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/runtime/server", "../../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime/server", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../../node_modules", "../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/node_modules", "../../layers/tairo/node_modules", "../../layers/core/node_modules", "../../layers/workforce-hub/node_modules", "../../layers/budget/node_modules", "../../layers/hr/node_modules", "../../layers/companies/node_modules", "../../layers/production/node_modules", "../../layers/accounting/node_modules", "../../layers/recruitment/node_modules", "../../layers/sales/node_modules", "../../layers/timemanagement/node_modules", "../../layers/communication/node_modules", "../../node_modules/.pnpm/reka-ui@2.3.0_typescript@5.8.3_vue@3.5.16_typescript@5.8.3_/node_modules/reka-ui/node_modules", "../../node_modules/.pnpm/@vueuse+nuxt@13.3.0_magicas_c83dd1e26dc7fa31b8d2951b71b982cc/node_modules/@vueuse/nuxt/node_modules", "../../node_modules/.pnpm/nuxt-component-meta@0.10.1_magicast@0.3.5/node_modules/nuxt-component-meta/node_modules", "../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/node_modules", "../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/node_modules", "../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/node_modules", "../../node_modules/.pnpm/@nuxt+content@3.4.0_magicast@0.3.5_typescript@5.8.3/node_modules/@nuxt/content/node_modules", "../../node_modules/.pnpm/@nuxt+fonts@0.11.4_db0@0.3._96a303972b0a0111acac0c3036fb3956/node_modules/@nuxt/fonts/node_modules", "../../node_modules/.pnpm/@pinia+nuxt@0.11.0_magicast_5c79cbd05b599f0ffb2fb3def69cce33/node_modules/@pinia/nuxt/node_modules", "../../node_modules/.pnpm/pinia-plugin-persistedstate_a6545990ae5be98b86260c735c5a3f67/node_modules/pinia-plugin-persistedstate/node_modules", "../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/node_modules", "../../../../AppData/Roaming/npm/node_modules/@nuxt/devtools/node_modules", "../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/node_modules", "../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/node_modules", "../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/node_modules", "../../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/node_modules", "../dist"]}