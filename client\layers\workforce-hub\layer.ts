import { defineNuxtModule, createResolver, addComponentsDir } from "@nuxt/kit";

export default defineNuxtModule({
  meta: {
    name: "workforce-hub",
    configKey: "workforceHub",
  },
  setup() {
    const { resolve } = createResolver(import.meta.url);

    // Add components directory
    addComponentsDir({
      path: resolve("./components"),
      pathPrefix: false,
    });

    // Add subdirectories for component groups
    addComponentsDir({
      path: resolve("./components/workforce"),
      pathPrefix: false,
    });

    addComponentsDir({
      path: resolve("./components/rental"),
      pathPrefix: false,
    });

    addComponentsDir({
      path: resolve("./components/assignments"),
      pathPrefix: false,
    });

    addComponentsDir({
      path: resolve("./components/matches"),
      pathPrefix: false,
    });
  },
});
