import type { ComponentData } from 'nuxt-component-meta'
export type NuxtComponentMetaNames = 'AccountMenu' | 'AddonApexcharts' | 'AddonInputPassword' | 'BaseLayerPage' | 'BaseLoader' | 'CalendarEvent' | 'CalendarEventPending' | 'CalendarSidebarCategories' | 'DemoPanelActivity' | 'DemoPanelInvest' | 'DemoPanelLanguage' | 'LanguageSelector' | 'LanguageToggle' | 'Logo' | 'LogoText' | 'MapMarker' | 'PasswordInputWithEye' | 'PhoneInput' | 'PwaInstallButton' | 'PwaInstallModal' | 'PwaThemeColor' | 'Sidebar' | 'SidebarBackdrop' | 'SidebarContent' | 'SidebarLayout' | 'SidebarLink' | 'SidebarLinks' | 'SidebarNav' | 'SidebarSubsidebar' | 'SidebarSubsidebarCollapsible' | 'SidebarSubsidebarCollapsibleLink' | 'SidebarSubsidebarCollapsibleTrigger' | 'SidebarSubsidebarContent' | 'SidebarSubsidebarHeader' | 'SidebarSubsidebarLink' | 'SidebarTrigger' | 'SubsidebarChat' | 'SubsidebarMessaging' | 'ThemeColorUpdater' | 'Toolbar' | 'AuthEmailVerification' | 'AuthPasswordInput' | 'AuthPaymentForm' | 'AuthRegistrationForm' | 'AuthRegistrationSuccess' | 'AuthRoleSelection' | 'AuthStripePaymentForm' | 'AuthSubscriptionPlanSelection' | 'TairoCheckAnimated' | 'TairoCheckboxAnimated' | 'TairoCheckboxCardIcon' | 'TairoContentWrapper' | 'TairoContentWrapperTabbed' | 'TairoError' | 'TairoFlexTable' | 'TairoFlexTableCell' | 'TairoFlexTableHeading' | 'TairoFlexTableRow' | 'TairoFormGroup' | 'TairoFormSave' | 'TairoFullscreenDropfile' | 'TairoImageZoom' | 'TairoInput' | 'TairoInputFileHeadless' | 'TairoMobileDrawer' | 'TairoPanels' | 'TairoRadioCard' | 'TairoSelect' | 'TairoSelectItem' | 'TairoTable' | 'TairoTableCell' | 'TairoTableHeading' | 'TairoTableRow' | 'TairoWelcome' | 'TairoCollapseBackdrop' | 'TairoCollapseCollapsible' | 'TairoCollapseCollapsibleLink' | 'TairoCollapseCollapsibleTrigger' | 'TairoCollapseContent' | 'TairoCollapseLayout' | 'TairoCollapseSidebar' | 'TairoCollapseSidebarClose' | 'TairoCollapseSidebarHeader' | 'TairoCollapseSidebarLink' | 'TairoCollapseSidebarLinks' | 'TairoMenu' | 'TairoMenuContent' | 'TairoMenuIndicator' | 'TairoMenuItem' | 'TairoMenuLink' | 'TairoMenuLinkTab' | 'TairoMenuList' | 'TairoMenuListItems' | 'TairoMenuTrigger' | 'TairoMenuViewport' | 'TairoSidebar' | 'TairoSidebarBackdrop' | 'TairoSidebarContent' | 'TairoSidebarLayout' | 'TairoSidebarLink' | 'TairoSidebarLinks' | 'TairoSidebarNav' | 'TairoSidebarSubsidebar' | 'TairoSidebarSubsidebarCollapsible' | 'TairoSidebarSubsidebarCollapsibleLink' | 'TairoSidebarSubsidebarCollapsibleTrigger' | 'TairoSidebarSubsidebarContent' | 'TairoSidebarSubsidebarHeader' | 'TairoSidebarSubsidebarLink' | 'TairoSidebarTrigger' | 'TairoSidenavBackdrop' | 'TairoSidenavCollapsible' | 'TairoSidenavCollapsibleLink' | 'TairoSidenavCollapsibleTrigger' | 'TairoSidenavContent' | 'TairoSidenavLayout' | 'TairoSidenavSidebar' | 'TairoSidenavSidebarDivider' | 'TairoSidenavSidebarHeader' | 'TairoSidenavSidebarLink' | 'TairoSidenavSidebarLinks' | 'TairoTopnavContent' | 'TairoTopnavHeader' | 'TairoTopnavLayout' | 'TairoTopnavNavbar' | 'BusinessRuleModal' | 'CoreDashboard' | 'ActivityFilters' | 'ActivityLogDetails' | 'ActivityLogItem' | 'ActivityLogList' | 'AiDecisionCard' | 'AiDecisionDetailModal' | 'ApiEndpointCard' | 'ApiEndpointDetailModal' | 'AutomationRuleCard' | 'BrandAssetCard' | 'ChartsBarChart' | 'ChartsLineChart' | 'ChartsPieChart' | 'ChartsRadialChart' | 'DashboardActivityCard' | 'DashboardBudgetAllocation' | 'DashboardEmployeeActivity' | 'DashboardMetricCard' | 'DashboardModuleOverview' | 'DashboardModuleUsageCard' | 'DashboardNotificationsCard' | 'DashboardProjectStatus' | 'DashboardRecentActivities' | 'DashboardSalesPerformance' | 'DashboardStatsOverview' | 'DashboardSystemHealth' | 'DashboardSystemHealthCard' | 'DashboardUpcomingEvents' | 'DashboardUserActivityCard' | 'DatabaseConnectionBuilderForm' | 'DatabaseConnectionPoolMonitor' | 'DatabaseConnectionStringForm' | 'DatabaseExplorer' | 'DatabaseHealth' | 'DatabaseSchema' | 'DatabaseTerminalWindow' | 'DocumentsDocumentCard' | 'DocumentsDocumentFilters' | 'DocumentsDocumentUploadModal' | 'EventsEventCard' | 'EventsEventDetailModal' | 'ExamplesComponentAccessExample' | 'HealthPerformanceMetricsCard' | 'HealthResourceUsageCard' | 'HealthServiceStatusCard' | 'LegalDocumentCard' | 'MlDatasetCard' | 'MlTrainingDataUploader' | 'MlTrainingFormatGuide' | 'ModulesModuleCard' | 'ModulesModuleDetailModal' | 'MonitoringAlertDetailsModal' | 'MonitoringAlertsListCard' | 'MonitoringAlertsSummaryCard' | 'MonitoringApiPerformanceCard' | 'MonitoringDatabaseResourcesCard' | 'MonitoringErrorDetailsModal' | 'MonitoringErrorLogsCard' | 'MonitoringErrorSummaryCard' | 'MonitoringPageLoadTimesCard' | 'MonitoringPerformanceMetricsCard' | 'MonitoringResourceOverviewCard' | 'MonitoringServerResourcesCard' | 'PoliciesPolicyCard' | 'PoliciesPolicyCategoryFilter' | 'PoliciesPolicyDetailModal' | 'RulesCategoryCard' | 'RulesRuleCard' | 'RulesRuleDetailModal' | 'RulesRuleFilters' | 'RulesTemplateCard' | 'RulesTemplateDetailModal' | 'RulesValidationTester' | 'SecurityAuditLogEntry' | 'SecurityAuditLogFilters' | 'SecurityEditRoleModal' | 'SecurityEditUserRolesModal' | 'SecurityRolePermissionsCard' | 'SecurityUserRolesTable' | 'SettingsAppearanceSettingsCard' | 'SettingsBackupHistoryCard' | 'SettingsBackupSettingsCard' | 'SettingsBackupStatusCard' | 'SettingsDeleteConfirmationModal' | 'SettingsEnvironmentInfoCard' | 'SettingsEnvironmentVariablesCard' | 'SettingsFormatSettingsCard' | 'SettingsGeneralSettingsCard' | 'SettingsLanguageSettingsCard' | 'SettingsPerformanceSettingsCard' | 'SettingsQuickActionsCard' | 'SettingsRestoreConfirmationModal' | 'SettingsRestoreFromFileModal' | 'SettingsRestoreSettingsCard' | 'WebhooksWebhookCard' | 'WebhooksWebhookDetailModal' | 'BudgetChartsSection' | 'BudgetDashboard' | 'BudgetIncomeForm' | 'DatePicker' | 'DemoAccountMenu' | 'DemoCalendarEvent' | 'DemoCalendarEventPending' | 'ExpensesSection' | 'GlobalFooter' | 'GlobalHeader' | 'IncomeSection' | 'TairoLogo' | 'ChartsBudgetChartsSection' | 'DashboardBudgetGoalsCard' | 'DashboardBudgetSummaryCard' | 'DashboardBudgetTrendsChart' | 'DashboardBudgetTypeSelector' | 'DashboardCategoryBreakdownCard' | 'DashboardRecentTransactionsCard' | 'ExpensesMultipleExpenses' | 'ExpensesOneTimeExpenses' | 'ExpensesRepeatedExpenses' | 'ForecastingCategories' | 'ForecastingChart' | 'ForecastingControls' | 'ForecastingScenarios' | 'ForecastingSummary' | 'IncomesMultipleIncomes' | 'IncomesOneTimeIncomes' | 'IncomesRepeatedIncomes' | 'PlannerBudgetComparisonCard' | 'PlannerMonthlyBudgetCard' | 'QuickBudgetCategoryBreakdown' | 'QuickBudgetEntryForm' | 'QuickBudgetExpensesList' | 'QuickBudgetGoals' | 'QuickBudgetIncomeList' | 'QuickBudgetSummaryCard' | 'QuickBudgetTransactionsList' | 'StatsSection' | 'TransactionsListCard' | 'EmployeeFilters' | 'EmployeeForm' | 'EmployeeList' | 'HrDashboard' | 'WorkerForm' | 'AttendanceRecordsTab' | 'AttendanceReportsTab' | 'AttendanceDailyAttendanceTab' | 'BenefitsBenefitPlansTab' | 'BenefitsBenefitSettingsTab' | 'BenefitsEmployeeBenefitsTab' | 'BonusesAllBonusesTab' | 'BonusesBonusReportsTab' | 'BonusesEmployeeBonusesTab' | 'CareerPathDetails' | 'CareerPathFormModal' | 'CareerPathOverview' | 'DepartmentsDepartmentCard' | 'DocumentsCompanyPoliciesTab' | 'DocumentsTab' | 'DocumentsEmployeeDocumentsTab' | 'EmployeesDocumentUploadForm' | 'EmployeesEmployeeDocuments' | 'EmployeesEmployeeLeave' | 'EmployeesEmployeeOverview' | 'EmployeesEmployeePayroll' | 'EmployeesEmployeePerformance' | 'EmployeesEmployeeProfileCard' | 'LeaveBalancesTab' | 'LeaveCalendarTab' | 'LeaveRequestsTab' | 'LeaveSettingsTab' | 'PayrollRunTab' | 'PayrollPayslipsTab' | 'PayrollTaxSettingsTab' | 'PerformanceEmployeePerformanceTab' | 'PerformanceAnalyticsTab' | 'PerformanceReviewsTab' | 'ShiftsShiftAssignmentsTab' | 'ShiftsShiftScheduleTab' | 'ShiftsShiftTypesTab' | 'TrainingCertificationsTab' | 'TrainingEmployeeTrainingTab' | 'TrainingEnrollEmployeesModal' | 'TrainingDetailsModal' | 'TrainingFormModal' | 'TrainingProgramCard' | 'TrainingProgramsTab' | 'CompaniesDashboard' | 'CompanyDashboard' | 'CompanyForm' | 'ClientsClientCard' | 'ClientsTable' | 'ComplianceAudits' | 'ComplianceOverview' | 'ComplianceRequirements' | 'RiskMitigationPlans' | 'RiskDashboard' | 'RiskRegister' | 'SettingsGeneralSettings' | 'SettingsIntegrationSettings' | 'SettingsNotificationSettings' | 'SettingsPermissionSettings' | 'SuppliersSupplierCard' | 'SuppliersSupplierDocuments' | 'SuppliersSupplierOrders' | 'SuppliersSupplierOverview' | 'SuppliersSupplierPerformance' | 'SuppliersSupplierPerformanceMetrics' | 'SuppliersSupplierTransactions' | 'SuppliersTable' | 'WorkforceCapacityPlanning' | 'WorkforceSkillsGapAnalysis' | 'WorkforceOverview' | 'ProductionDashboard' | 'DashboardTaskStatusChart' | 'DocumentsUploadDocumentModal' | 'DocumentsViewDocumentModal' | 'QualityChecklistCard' | 'QualityChecklistDetailModal' | 'QualityChecklistItem' | 'QualityNewChecklistModal' | 'QualityPhotoCard' | 'QualityPhotoDetailModal' | 'QualityUploadPhotoModal' | 'ReportsBarChart' | 'ReportsLineChart' | 'ReportsMetricCard' | 'ReportsPieChart' | 'ReportsReportCard' | 'ResourcesEquipmentCard' | 'ResourcesEquipmentDetailModal' | 'ResourcesMaterialCard' | 'ResourcesMaterialDetailModal' | 'ResourcesNewEquipmentModal' | 'ResourcesNewMaterialModal' | 'ResourcesNewWorkerModal' | 'ResourcesWorkforceCard' | 'ResourcesWorkforceDetailModal' | 'StatisticsProjectMetricsChart' | 'StatisticsProjectStatusChart' | 'StatisticsTaskCompletionChart' | 'TasksNewTaskModal' | 'TasksTaskBoard' | 'TasksTaskCard' | 'TasksTaskColumn' | 'TasksTaskDetailModal' | 'TimelineGanttChart' | 'AccountingDashboard' | 'JournalEntryForm' | 'RecruitmentSidebarItem' | 'AdminLanguageManager' | 'AdminTranslationsManager' | 'FormsFormBuilderFieldEditor' | 'FormsFormBuilderFieldList' | 'FormsFormBuilderPreview' | 'FormsFormBuilderPreviewField' | 'FormsFormBuilderSettings' | 'FormsFormBuilderSidebar' | 'FormsFormBuilderStepEditor' | 'FormsPublicFormField' | 'WorkforceFormAdditionalInfoStep' | 'WorkforceFormConfirmationStep' | 'WorkforceFormIntroductionStep' | 'WorkforceFormLanguageSelector' | 'WorkforceFormPersonalInfoStep' | 'WorkforceFormProfessionalExperienceStep' | 'WorkforceFormQualificationsStep' | 'WorkforceFormReferencesStep' | 'WorkforceFormSpecialtiesStep' | 'WorkforceAssignmentDetailsModal' | 'WorkforceAssignmentFilters' | 'WorkforceAssignmentsPagination' | 'WorkforceJobDetailsModal' | 'WorkforceJobFilters' | 'WorkforceJobFormModal' | 'WorkforceJobsPagination' | 'WorkforceMatchDetailsModal' | 'WorkforceMatchFilters' | 'WorkforceMatchesPagination' | 'WorkforceWorkerDetailsModal' | 'WorkforceWorkerFormModal' | 'WorkforceWorkersPagination' | 'ActivitiesActivityFormModal' | 'ActivitiesActivityViewModal' | 'AnalyticsDateRangeFilter' | 'AnalyticsForecastScenariosChart' | 'AnalyticsForecastSummaryCard' | 'AnalyticsGoalTrackingChart' | 'AnalyticsKpiCard' | 'AnalyticsReportCategoriesList' | 'AnalyticsReportFormModal' | 'AnalyticsReportViewModal' | 'AnalyticsReportsList' | 'AnalyticsRevenueForecastChart' | 'AnalyticsRevenueTrendChart' | 'AnalyticsSalesByCategoryChart' | 'AnalyticsSalesByRepresentativeChart' | 'AnalyticsSalesPipelineChart' | 'AnalyticsScheduleReportModal' | 'AnalyticsWinProbabilityChart' | 'CampaignsAddPaymentMethodModal' | 'CampaignsCampaignCard' | 'CampaignsCampaignCreateModal' | 'CampaignsCampaignEditModal' | 'CampaignsCampaignViewModal' | 'CampaignsEmailTemplateFormModal' | 'CampaignsEmailTemplateViewModal' | 'CampaignsStepsCampaignAiGenerationStep' | 'CampaignsStepsCampaignBriefStep' | 'CampaignsStepsCampaignDetailsStep' | 'CampaignsStepsCampaignPaymentStep' | 'CampaignsStepsCampaignReviewStep' | 'CommonSalesQuickActionButton' | 'DashboardSalesPipelineChart' | 'DealsDealProductsTable' | 'ModalsDealQuickCreateModal' | 'ModalsLeadConvertModal' | 'ModalsLeadQuickCreateModal' | 'ModalsMeetingQuickCreateModal' | 'ProductsProductFormModal' | 'ProductsProductViewModal' | 'QuotationsQuotationEmptyState' | 'QuotationsQuotationFilters' | 'QuotationsQuotationFormModal' | 'QuotationsQuotationList' | 'QuotationsQuotationViewModal' | 'QuotationsQuoteTemplateFormModal' | 'QuotationsQuoteTemplateViewModal' | 'SettingsApiKeyItem' | 'SettingsGoalCard' | 'SettingsGoalFormModal' | 'SettingsIntegrationCard' | 'SettingsIntegrationConfigModal' | 'SettingsStageFormModal' | 'SettingsWorkflowCard' | 'SettingsWorkflowFormModal' | 'SettingsWorkflowLogsModal' | 'BasePagination' | 'AnalyticsBillableHoursChart' | 'AnalyticsProjectDistributionChart' | 'AnalyticsTeamProductivityChart' | 'AnalyticsTimeTrendsChart' | 'AssignmentsAssignmentDetailsModal' | 'AssignmentsCreateAssignmentModal' | 'AssignmentsEditAssignmentModal' | 'CalendarAddEventModal' | 'CalendarControls' | 'CalendarDayView' | 'CalendarEventDetailsModal' | 'CalendarMonthView' | 'CalendarTeamView' | 'CalendarWeekView' | 'ClientReportsClientReportDetailModal' | 'ClientReportsClientReportFilters' | 'ClientReportsCreateClientReportModal' | 'DashboardActivityItem' | 'DashboardHoursByProjectChart' | 'DashboardHoursByTeamMemberChart' | 'DashboardSummaryCard' | 'EntriesTimeEntryFormModal' | 'LocationsLocationAlertItem' | 'LocationsWorkLocationItem' | 'LocationsWorkerLocationItem' | 'MapComponent' | 'SettingsLocationFormModal' | 'SettingsLocationPickerMap' | 'SettingsMapComponent' | 'SettingsWorkLocationSettingsItem' | 'SettingsApprovalsApprovalSettings' | 'SettingsApprovalsDeleteWorkflowModal' | 'SettingsApprovalsWorkflowFormModal' | 'SettingsApprovalsWorkflowList' | 'SettingsIntegrationsIntegrationCard' | 'SettingsIntegrationsIntegrationConfigModal' | 'SettingsIntegrationsSyncHistoryList' | 'TeamMemberDetails' | 'TeamMemberItem' | 'TimesheetsCreateTimesheetModal' | 'TimesheetsTimesheetDetailModal' | 'TimesheetsTimesheetFilters' | 'TimesheetsTimesheetList' | 'TimesheetsTimesheetSummary' | 'WorkerAssignmentItem' | 'WorkerCurrentAssignmentCard' | 'WorkerMapComponent' | 'WorkerProjectSelectionModal' | 'WorkerTimeEntryItem' | 'WorkerTimeTrackingButton' | 'WorkerTimeTrackingModal' | 'WorkerWeeklyHoursChart' | 'EndpointsEndpointDetailsModal' | 'EndpointsEndpointFieldEditor' | 'ExamplesAddonsDatepicker' | 'ExamplesAddonsMapbox' | 'ExamplesApexchartsBase' | 'ExamplesFlexTableCurved' | 'ExamplesFlexTableRounded' | 'ExamplesFlexTableSmooth' | 'ExamplesFlexTableStraight' | 'ExamplesInputPasswordBase' | 'ExamplesInputPasswordDisabled' | 'ExamplesInputPasswordLocale' | 'ExamplesInputPasswordUserInput' | 'ExamplesInputPasswordValidation' | 'ExamplesInputPhoneBase' | 'ExamplesInputPhoneCountry' | 'ExamplesInputPhoneDisabled' | 'ExamplesInputPhoneFormat' | 'ExamplesInputPhoneShape' | 'ExamplesInputPhoneSize' | 'ExamplesInputPhoneValidation' | 'ExamplesLightweightChartsBase' | 'ExamplesPanelActivity' | 'ExamplesPanelLanguage' | 'ExamplesPanelSearch' | 'ExamplesPanelTask' | 'ExamplesTableCurved' | 'ExamplesTableMediaCurved' | 'ExamplesTableMediaRounded' | 'ExamplesTableMediaSmooth' | 'ExamplesTableMediaStraight' | 'ExamplesTableRounded' | 'ExamplesTableSmooth' | 'ExamplesTableStraight' | 'ExamplesTairoCheckAnimated' | 'ExamplesTairoCheckboxAnimated' | 'ExamplesTairoCheckboxCardIcon' | 'ExamplesTairoCircularMenu' | 'ExamplesTairoError' | 'ExamplesTairoFormGroup' | 'ExamplesTairoFormSave' | 'ExamplesTairoInput' | 'ExamplesTairoLogo' | 'ExamplesTairoLogotext' | 'ExamplesTairoMenuComplete' | 'ExamplesTairoMenu' | 'ExamplesTairoMobileDrawer' | 'ExamplesTairoRadioCard' | 'ExamplesTairoSelect' | 'ExamplesTairoValidation' | 'ProseA' | 'ProseBlockquote' | 'ProseCode' | 'ProseEm' | 'ProseH1' | 'ProseH2' | 'ProseH3' | 'ProseH4' | 'ProseH5' | 'ProseH6' | 'ProseHr' | 'ProseImg' | 'ProseLi' | 'ProseOl' | 'ProseP' | 'ProsePre' | 'ProseScript' | 'ProseStrong' | 'ProseTable' | 'ProseTbody' | 'ProseTd' | 'ProseTh' | 'ProseThead' | 'ProseTr' | 'ProseUl' | 'BaseAccordion' | 'BaseAccordionItem' | 'BaseAutocomplete' | 'BaseAutocompleteGroup' | 'BaseAutocompleteItem' | 'BaseAutocompleteLabel' | 'BaseAutocompleteSeparator' | 'BaseAvatar' | 'BaseAvatarGroup' | 'BaseBreadcrumb' | 'BaseButton' | 'BaseCard' | 'BaseCheckbox' | 'BaseCheckboxGroup' | 'BaseChip' | 'BaseDropdown' | 'BaseDropdownArrow' | 'BaseDropdownCheckbox' | 'BaseDropdownItem' | 'BaseDropdownLabel' | 'BaseDropdownRadioGroup' | 'BaseDropdownRadioItem' | 'BaseDropdownSeparator' | 'BaseDropdownSub' | 'BaseField' | 'BaseHeading' | 'BaseIconBox' | 'BaseInput' | 'BaseInputFile' | 'BaseInputNumber' | 'BaseKbd' | 'BaseLink' | 'BaseList' | 'BaseListItem' | 'BaseMessage' | 'BasePaginationButtonFirst' | 'BasePaginationButtonLast' | 'BasePaginationButtonNext' | 'BasePaginationButtonPrev' | 'BasePaginationItems' | 'BaseParagraph' | 'BasePlaceholderPage' | 'BasePlaceload' | 'BasePopover' | 'BasePrimitiveField' | 'BasePrimitiveFieldController' | 'BasePrimitiveFieldDescription' | 'BasePrimitiveFieldError' | 'BasePrimitiveFieldErrorIndicator' | 'BasePrimitiveFieldLabel' | 'BasePrimitiveFieldLoadingIndicator' | 'BasePrimitiveFieldRequiredIndicator' | 'BasePrimitiveFieldSuccessIndicator' | 'BaseProgress' | 'BaseProgressCircle' | 'BaseProse' | 'BaseProviders' | 'BaseRadio' | 'BaseRadioGroup' | 'BaseSelect' | 'BaseSelectGroup' | 'BaseSelectItem' | 'BaseSelectLabel' | 'BaseSelectSeparator' | 'BaseSlider' | 'BaseSnack' | 'BaseSwitchBall' | 'BaseSwitchThin' | 'BaseTabs' | 'BaseTabsContent' | 'BaseTabsTrigger' | 'BaseTag' | 'BaseText' | 'BaseTextarea' | 'BaseThemeSwitch' | 'BaseThemeSystem' | 'BaseThemeToggle' | 'BaseToast' | 'BaseToastProvider' | 'BaseTooltip' | 'NuxtWelcome' | 'NuxtLayout' | 'NuxtErrorBoundary' | 'ClientOnly' | 'DevOnly' | 'ServerPlaceholder' | 'NuxtLink' | 'NuxtLoadingIndicator' | 'NuxtRouteAnnouncer' | 'NuxtImg' | 'NuxtPicture'
export type NuxtComponentMeta = Record<NuxtComponentMetaNames, ComponentData>
declare const components: NuxtComponentMeta
export { components as default, components }