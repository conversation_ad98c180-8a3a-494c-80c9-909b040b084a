
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  firstName: 'firstName',
  lastName: 'lastName',
  birthdate: 'birthdate',
  phone: 'phone',
  address: 'address',
  address2: 'address2',
  street: 'street',
  city: 'city',
  postalCode: 'postalCode',
  state: 'state',
  country: 'country',
  notes: 'notes',
  avatar: 'avatar',
  coverImage: 'coverImage',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  emailConfirmationToken: 'emailConfirmationToken',
  emailConfirmed: 'emailConfirmed',
  phoneConfirmed: 'phoneConfirmed',
  referredByUserId: 'referredByUserId',
  googleId: 'googleId',
  barcode: 'barcode',
  qrCode: 'qrCode',
  stripeCustomerId: 'stripeCustomerId'
};

exports.Prisma.SessionLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  loginAt: 'loginAt',
  logoutAt: 'logoutAt'
};

exports.Prisma.UserRoleScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserCompanyScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  role: 'role',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  status: 'status',
  logo: 'logo',
  coverImage: 'coverImage',
  creditRating: 'creditRating',
  contractTerms: 'contractTerms',
  website: 'website',
  email: 'email',
  phone: 'phone',
  registrationNumber: 'registrationNumber',
  vatNumber: 'vatNumber',
  industry: 'industry',
  description: 'description',
  foundedYear: 'foundedYear',
  employeeCount: 'employeeCount',
  annualRevenue: 'annualRevenue',
  isIndividual: 'isIndividual',
  address: 'address',
  address2: 'address2',
  city: 'city',
  postalCode: 'postalCode',
  state: 'state',
  country: 'country',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  barcode: 'barcode',
  qrCode: 'qrCode',
  becameUserAt: 'becameUserAt',
  previousTransactions: 'previousTransactions'
};

exports.Prisma.WorkforceNeedScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  skillsNeeded: 'skillsNeeded',
  quantity: 'quantity',
  duration: 'duration',
  budget: 'budget',
  priority: 'priority',
  status: 'status',
  aiMatches: 'aiMatches'
};

exports.Prisma.FinancialHealthScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  paymentHistory: 'paymentHistory',
  stabilityIndex: 'stabilityIndex',
  riskLevel: 'riskLevel',
  lastAssessment: 'lastAssessment'
};

exports.Prisma.ClientRelationshipScalarFieldEnum = {
  id: 'id',
  clientCompanyId: 'clientCompanyId',
  providerCompanyId: 'providerCompanyId',
  status: 'status',
  startDate: 'startDate',
  endDate: 'endDate',
  contractValue: 'contractValue',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lifetimeValue: 'lifetimeValue',
  lastTransactionDate: 'lastTransactionDate',
  transactionCount: 'transactionCount'
};

exports.Prisma.SupplierRelationshipScalarFieldEnum = {
  id: 'id',
  buyerCompanyId: 'buyerCompanyId',
  supplierCompanyId: 'supplierCompanyId',
  status: 'status',
  startDate: 'startDate',
  endDate: 'endDate',
  contractValue: 'contractValue',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lifetimeValue: 'lifetimeValue',
  lastTransactionDate: 'lastTransactionDate',
  transactionCount: 'transactionCount',
  reliabilityScore: 'reliabilityScore',
  qualityScore: 'qualityScore'
};

exports.Prisma.CompanyTransitionScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  userId: 'userId',
  previousType: 'previousType',
  transitionDate: 'transitionDate',
  transitionReason: 'transitionReason',
  previousData: 'previousData',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProjectProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  projectId: 'projectId'
};

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  status: 'status',
  startDate: 'startDate',
  endDate: 'endDate',
  companyId: 'companyId',
  clientId: 'clientId',
  projectLeadId: 'projectLeadId',
  budget: 'budget',
  barcode: 'barcode',
  qrCode: 'qrCode',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TaskScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  companyId: 'companyId',
  name: 'name',
  description: 'description',
  status: 'status',
  assignedTo: 'assignedTo',
  startDate: 'startDate',
  endDate: 'endDate',
  priority: 'priority',
  qrCode: 'qrCode',
  barcode: 'barcode'
};

exports.Prisma.QualityChecklistScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  companyId: 'companyId',
  name: 'name',
  createdById: 'createdById',
  checkedById: 'checkedById'
};

exports.Prisma.QualityStepScalarFieldEnum = {
  id: 'id',
  taskId: 'taskId',
  checklistId: 'checklistId',
  companyId: 'companyId',
  name: 'name',
  description: 'description',
  required: 'required',
  completed: 'completed'
};

exports.Prisma.PhotoScalarFieldEnum = {
  id: 'id',
  stepId: 'stepId',
  companyId: 'companyId',
  url: 'url',
  timestamp: 'timestamp'
};

exports.Prisma.DocumentScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  taskId: 'taskId',
  companyId: 'companyId',
  name: 'name',
  url: 'url',
  type: 'type',
  description: 'description',
  size: 'size',
  mimeType: 'mimeType',
  uploadedBy: 'uploadedBy',
  category: 'category',
  module: 'module',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StageScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  projectId: 'projectId'
};

exports.Prisma.WorkerScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  address: 'address',
  rating: 'rating',
  status: 'status',
  currentCompanyId: 'currentCompanyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  laborRequestId: 'laborRequestId'
};

exports.Prisma.LaborRequestScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  requirements: 'requirements',
  duration: 'duration',
  quantity: 'quantity',
  status: 'status',
  aiScore: 'aiScore'
};

exports.Prisma.ContractScalarFieldEnum = {
  id: 'id',
  workerId: 'workerId',
  companyId: 'companyId',
  terms: 'terms',
  duration: 'duration',
  status: 'status'
};

exports.Prisma.SkillScalarFieldEnum = {
  id: 'id',
  name: 'name',
  category: 'category',
  level: 'level',
  verified: 'verified',
  verifiedBy: 'verifiedBy'
};

exports.Prisma.CertificationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  issuer: 'issuer',
  validUntil: 'validUntil',
  document: 'document',
  verified: 'verified',
  workerId: 'workerId'
};

exports.Prisma.WorkerRentalScalarFieldEnum = {
  id: 'id',
  workerId: 'workerId',
  fromCompanyId: 'fromCompanyId',
  toCompanyId: 'toCompanyId',
  startDate: 'startDate',
  endDate: 'endDate',
  status: 'status',
  hourlyRate: 'hourlyRate',
  mediationFee: 'mediationFee',
  contract: 'contract'
};

exports.Prisma.WorkerDocumentScalarFieldEnum = {
  id: 'id',
  workerId: 'workerId',
  type: 'type',
  url: 'url',
  verified: 'verified',
  uploadedAt: 'uploadedAt'
};

exports.Prisma.WorkerAvailabilityScalarFieldEnum = {
  id: 'id',
  workerId: 'workerId',
  schedule: 'schedule',
  notice: 'notice',
  minHours: 'minHours',
  maxHours: 'maxHours',
  preferences: 'preferences'
};

exports.Prisma.FeedbackScalarFieldEnum = {
  id: 'id',
  workerId: 'workerId',
  rating: 'rating',
  comment: 'comment',
  createdAt: 'createdAt'
};

exports.Prisma.HealthCheckScalarFieldEnum = {
  id: 'id',
  workerId: 'workerId',
  date: 'date',
  status: 'status',
  notes: 'notes'
};

exports.Prisma.SalaryInfoScalarFieldEnum = {
  id: 'id',
  workerId: 'workerId',
  amount: 'amount',
  currency: 'currency',
  effectiveDate: 'effectiveDate'
};

exports.Prisma.TimeTrackingLogScalarFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  startTime: 'startTime',
  endTime: 'endTime',
  duration: 'duration',
  notes: 'notes'
};

exports.Prisma.EmployeeScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  position: 'position',
  department: 'department',
  startDate: 'startDate',
  endDate: 'endDate',
  salary: 'salary',
  status: 'status',
  employmentType: 'employmentType',
  emergencyContact: 'emergencyContact',
  emergencyPhone: 'emergencyPhone',
  managerUserId: 'managerUserId',
  managerId: 'managerId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DepartmentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  companyId: 'companyId',
  managerId: 'managerId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PositionScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  departmentId: 'departmentId',
  minSalary: 'minSalary',
  maxSalary: 'maxSalary',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LeaveRequestScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  startDate: 'startDate',
  endDate: 'endDate',
  leaveType: 'leaveType',
  reason: 'reason',
  status: 'status',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LeaveBalanceScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  leaveType: 'leaveType',
  balance: 'balance',
  year: 'year',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AttendanceScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  date: 'date',
  clockIn: 'clockIn',
  clockOut: 'clockOut',
  status: 'status',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ShiftScalarFieldEnum = {
  id: 'id',
  name: 'name',
  startTime: 'startTime',
  endTime: 'endTime',
  employeeId: 'employeeId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PayrollScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  periodStart: 'periodStart',
  periodEnd: 'periodEnd',
  basicSalary: 'basicSalary',
  allowances: 'allowances',
  deductions: 'deductions',
  taxes: 'taxes',
  netSalary: 'netSalary',
  status: 'status',
  processedBy: 'processedBy',
  processedAt: 'processedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmployeeBenefitBasicScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  benefitType: 'benefitType',
  provider: 'provider',
  policyNumber: 'policyNumber',
  startDate: 'startDate',
  endDate: 'endDate',
  amount: 'amount',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BonusBasicScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  bonusType: 'bonusType',
  amount: 'amount',
  date: 'date',
  reason: 'reason',
  approvedBy: 'approvedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TrainingBasicScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  employeeId: 'employeeId',
  startDate: 'startDate',
  endDate: 'endDate',
  status: 'status',
  provider: 'provider',
  cost: 'cost',
  certificateUrl: 'certificateUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CareerPathBasicScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  currentLevel: 'currentLevel',
  targetPosition: 'targetPosition',
  skills: 'skills',
  timeline: 'timeline',
  mentorId: 'mentorId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BenefitScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  provider: 'provider',
  policyNumber: 'policyNumber',
  coverage: 'coverage',
  premium: 'premium',
  employerContribution: 'employerContribution',
  employeeContribution: 'employeeContribution',
  startDate: 'startDate',
  endDate: 'endDate',
  status: 'status',
  documents: 'documents',
  companyId: 'companyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmployeeBenefitScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  benefitId: 'benefitId',
  enrollmentDate: 'enrollmentDate',
  effectiveDate: 'effectiveDate',
  coverageAmount: 'coverageAmount',
  premium: 'premium',
  employeeContribution: 'employeeContribution',
  dependents: 'dependents',
  notes: 'notes',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BonusScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  type: 'type',
  amount: 'amount',
  currency: 'currency',
  description: 'description',
  awardDate: 'awardDate',
  paymentDate: 'paymentDate',
  isPaid: 'isPaid',
  approvedBy: 'approvedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PerformanceReviewScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  reviewerId: 'reviewerId',
  reviewDate: 'reviewDate',
  rating: 'rating',
  strengths: 'strengths',
  weaknesses: 'weaknesses',
  goals: 'goals',
  feedback: 'feedback',
  employeeComments: 'employeeComments',
  isAcknowledged: 'isAcknowledged',
  acknowledgedAt: 'acknowledgedAt',
  nextReviewDate: 'nextReviewDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TrainingScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  type: 'type',
  provider: 'provider',
  location: 'location',
  startDate: 'startDate',
  endDate: 'endDate',
  duration: 'duration',
  cost: 'cost',
  materials: 'materials',
  status: 'status',
  companyId: 'companyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmployeeTrainingScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  trainingId: 'trainingId',
  enrollmentDate: 'enrollmentDate',
  completionDate: 'completionDate',
  status: 'status',
  score: 'score',
  certificate: 'certificate',
  feedback: 'feedback',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CareerPathScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  currentPosition: 'currentPosition',
  targetPosition: 'targetPosition',
  skills: 'skills',
  timeline: 'timeline',
  mentorId: 'mentorId',
  developmentPlan: 'developmentPlan',
  progressNotes: 'progressNotes',
  lastReviewDate: 'lastReviewDate',
  nextReviewDate: 'nextReviewDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.HrDocumentScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  category: 'category',
  fileUrl: 'fileUrl',
  fileType: 'fileType',
  fileSize: 'fileSize',
  isConfidential: 'isConfidential',
  expiryDate: 'expiryDate',
  uploadedBy: 'uploadedBy',
  companyId: 'companyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmployeeDocumentScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  documentId: 'documentId',
  accessLevel: 'accessLevel',
  isAcknowledged: 'isAcknowledged',
  acknowledgedAt: 'acknowledgedAt',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BusinessRuleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  condition: 'condition',
  action: 'action',
  severity: 'severity',
  priority: 'priority',
  isActive: 'isActive',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BusinessStrategyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  parameters: 'parameters',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AiDecisionLogScalarFieldEnum = {
  id: 'id',
  module: 'module',
  action: 'action',
  context: 'context',
  rules: 'rules',
  suggestion: 'suggestion',
  severity: 'severity',
  timestamp: 'timestamp'
};

exports.Prisma.SystemIntegrationScalarFieldEnum = {
  id: 'id',
  moduleName: 'moduleName',
  isEnabled: 'isEnabled',
  config: 'config',
  dependencies: 'dependencies'
};

exports.Prisma.ContactScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phone: 'phone',
  companyId: 'companyId'
};

exports.Prisma.AgreementScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  companyId: 'companyId'
};

exports.Prisma.RiskAssessmentScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  score: 'score',
  details: 'details',
  lastUpdated: 'lastUpdated'
};

exports.Prisma.PageAccessRightScalarFieldEnum = {
  id: 'id',
  path: 'path',
  module: 'module',
  role: 'role',
  canAccess: 'canAccess',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  name: 'name',
  code: 'code',
  type: 'type',
  balance: 'balance',
  parentAccountId: 'parentAccountId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  metadata: 'metadata'
};

exports.Prisma.JournalEntryScalarFieldEnum = {
  id: 'id',
  date: 'date',
  description: 'description',
  amount: 'amount',
  debitAccountId: 'debitAccountId',
  creditAccountId: 'creditAccountId',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  metadata: 'metadata'
};

exports.Prisma.IncomeScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  name: 'name',
  description: 'description',
  amount: 'amount',
  paymentMethod: 'paymentMethod',
  source: 'source',
  interval: 'interval',
  startDate: 'startDate',
  endDate: 'endDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  paid: 'paid',
  barcode: 'barcode',
  qrCode: 'qrCode',
  category: 'category',
  notes: 'notes',
  budgetId: 'budgetId'
};

exports.Prisma.ExpenseScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  name: 'name',
  description: 'description',
  amount: 'amount',
  paymentMethod: 'paymentMethod',
  source: 'source',
  interval: 'interval',
  startDate: 'startDate',
  endDate: 'endDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  paid: 'paid',
  barcode: 'barcode',
  qrCode: 'qrCode',
  category: 'category',
  notes: 'notes',
  budgetId: 'budgetId'
};

exports.Prisma.AssetScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  method: 'method',
  customName: 'customName',
  initialAmount: 'initialAmount',
  currentAmount: 'currentAmount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  barcode: 'barcode',
  qrCode: 'qrCode',
  notes: 'notes'
};

exports.Prisma.BudgetScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  name: 'name',
  description: 'description',
  startDate: 'startDate',
  endDate: 'endDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isActive: 'isActive',
  type: 'type'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  type: 'type',
  description: 'description',
  amount: 'amount',
  category: 'category',
  date: 'date',
  status: 'status',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BudgetGoalScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  name: 'name',
  description: 'description',
  targetAmount: 'targetAmount',
  currentAmount: 'currentAmount',
  startDate: 'startDate',
  endDate: 'endDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  type: 'type',
  status: 'status'
};

exports.Prisma.EmailAccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  email: 'email',
  name: 'name',
  provider: 'provider',
  isDefault: 'isDefault',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmailMessageScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  folderId: 'folderId',
  conversationId: 'conversationId',
  from: 'from',
  to: 'to',
  cc: 'cc',
  bcc: 'bcc',
  subject: 'subject',
  body: 'body',
  isHtml: 'isHtml',
  status: 'status',
  priority: 'priority',
  hasAttachments: 'hasAttachments',
  receivedAt: 'receivedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmailAttachmentScalarFieldEnum = {
  id: 'id',
  messageId: 'messageId',
  name: 'name',
  type: 'type',
  size: 'size',
  url: 'url',
  createdAt: 'createdAt'
};

exports.Prisma.EmailFolderScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  name: 'name',
  type: 'type',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ChatConversationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ChatParticipantScalarFieldEnum = {
  id: 'id',
  conversationId: 'conversationId',
  userId: 'userId',
  joinedAt: 'joinedAt',
  leftAt: 'leftAt'
};

exports.Prisma.ChatMessageScalarFieldEnum = {
  id: 'id',
  conversationId: 'conversationId',
  senderId: 'senderId',
  content: 'content',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CommunicationEndpointScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  name: 'name',
  description: 'description',
  path: 'path',
  method: 'method',
  apiKey: 'apiKey',
  status: 'status',
  fields: 'fields',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EndpointRequestScalarFieldEnum = {
  id: 'id',
  endpointId: 'endpointId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  requestData: 'requestData',
  responseData: 'responseData',
  statusCode: 'statusCode',
  processingTime: 'processingTime',
  isRead: 'isRead',
  createdAt: 'createdAt'
};

exports.Prisma.SubscriptionPackageScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  price: 'price',
  durationInDays: 'durationInDays',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LegacySubscriptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  packageId: 'packageId',
  startDate: 'startDate',
  endDate: 'endDate',
  price: 'price',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubscriptionPlanScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  monthlyPrice: 'monthlyPrice',
  yearlyPrice: 'yearlyPrice',
  maxUsers: 'maxUsers',
  additionalUserFee: 'additionalUserFee',
  features: 'features',
  modules: 'modules',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  planId: 'planId',
  status: 'status',
  currentUsers: 'currentUsers',
  startDate: 'startDate',
  endDate: 'endDate',
  trialEndDate: 'trialEndDate',
  billingCycle: 'billingCycle',
  lastBillingDate: 'lastBillingDate',
  nextBillingDate: 'nextBillingDate',
  stripeCustomerId: 'stripeCustomerId',
  stripeSubscriptionId: 'stripeSubscriptionId',
  cancelAtPeriodEnd: 'cancelAtPeriodEnd',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  subscriptionId: 'subscriptionId',
  amount: 'amount',
  currency: 'currency',
  status: 'status',
  stripePaymentId: 'stripePaymentId',
  stripeInvoiceId: 'stripeInvoiceId',
  paymentMethod: 'paymentMethod',
  paymentDate: 'paymentDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isSupporterPayment: 'isSupporterPayment',
  supporterUserId: 'supporterUserId',
  packageType: 'packageType',
  packageAmount: 'packageAmount'
};

exports.Prisma.LeadScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  company: 'company',
  jobTitle: 'jobTitle',
  status: 'status',
  source: 'source',
  notes: 'notes',
  assignedToId: 'assignedToId',
  score: 'score',
  estimatedValue: 'estimatedValue',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  convertedAt: 'convertedAt'
};

exports.Prisma.LeadTagScalarFieldEnum = {
  id: 'id',
  leadId: 'leadId',
  name: 'name',
  createdAt: 'createdAt'
};

exports.Prisma.LeadCustomFieldScalarFieldEnum = {
  id: 'id',
  leadId: 'leadId',
  name: 'name',
  value: 'value',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DealScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  value: 'value',
  currency: 'currency',
  stage: 'stage',
  type: 'type',
  probability: 'probability',
  expectedCloseDate: 'expectedCloseDate',
  actualCloseDate: 'actualCloseDate',
  leadId: 'leadId',
  companyId: 'companyId',
  assignedToId: 'assignedToId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DealProductScalarFieldEnum = {
  id: 'id',
  dealId: 'dealId',
  productId: 'productId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  discount: 'discount',
  total: 'total',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DealNoteScalarFieldEnum = {
  id: 'id',
  dealId: 'dealId',
  content: 'content',
  createdById: 'createdById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ActivityScalarFieldEnum = {
  id: 'id',
  type: 'type',
  subject: 'subject',
  description: 'description',
  status: 'status',
  startDate: 'startDate',
  endDate: 'endDate',
  location: 'location',
  leadId: 'leadId',
  dealId: 'dealId',
  companyId: 'companyId',
  assignedToId: 'assignedToId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  completedAt: 'completedAt',
  outcome: 'outcome'
};

exports.Prisma.CampaignScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  status: 'status',
  budget: 'budget',
  actualCost: 'actualCost',
  startDate: 'startDate',
  endDate: 'endDate',
  targetAudience: 'targetAudience',
  expectedRevenue: 'expectedRevenue',
  actualRevenue: 'actualRevenue',
  createdById: 'createdById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CampaignLeadScalarFieldEnum = {
  id: 'id',
  campaignId: 'campaignId',
  leadId: 'leadId',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CampaignMetricScalarFieldEnum = {
  id: 'id',
  campaignId: 'campaignId',
  name: 'name',
  value: 'value',
  date: 'date',
  createdAt: 'createdAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  sku: 'sku',
  category: 'category',
  price: 'price',
  currency: 'currency',
  cost: 'cost',
  taxRate: 'taxRate',
  isActive: 'isActive',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuotationScalarFieldEnum = {
  id: 'id',
  quotationNumber: 'quotationNumber',
  title: 'title',
  dealId: 'dealId',
  companyId: 'companyId',
  contactName: 'contactName',
  contactEmail: 'contactEmail',
  contactPhone: 'contactPhone',
  status: 'status',
  issueDate: 'issueDate',
  expiryDate: 'expiryDate',
  subtotal: 'subtotal',
  taxAmount: 'taxAmount',
  discountAmount: 'discountAmount',
  totalAmount: 'totalAmount',
  terms: 'terms',
  notes: 'notes',
  createdById: 'createdById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  sentAt: 'sentAt',
  viewedAt: 'viewedAt',
  acceptedAt: 'acceptedAt',
  rejectedAt: 'rejectedAt'
};

exports.Prisma.QuotationItemScalarFieldEnum = {
  id: 'id',
  quotationId: 'quotationId',
  productId: 'productId',
  description: 'description',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  discount: 'discount',
  taxRate: 'taxRate',
  total: 'total',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SalesGoalScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  targetAmount: 'targetAmount',
  currency: 'currency',
  startDate: 'startDate',
  endDate: 'endDate',
  assignedToId: 'assignedToId',
  goalType: 'goalType',
  progress: 'progress',
  isAchieved: 'isAchieved',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SalesPipelineStageScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  order: 'order',
  probability: 'probability',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TimeEntryScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  projectId: 'projectId',
  taskId: 'taskId',
  companyId: 'companyId',
  startTime: 'startTime',
  endTime: 'endTime',
  duration: 'duration',
  description: 'description',
  status: 'status',
  approved: 'approved',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt',
  notes: 'notes',
  startLocationId: 'startLocationId',
  endLocationId: 'endLocationId',
  startLocationCompliant: 'startLocationCompliant',
  endLocationCompliant: 'endLocationCompliant',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  timesheetId: 'timesheetId'
};

exports.Prisma.LocationScalarFieldEnum = {
  id: 'id',
  latitude: 'latitude',
  longitude: 'longitude',
  accuracy: 'accuracy',
  address: 'address',
  timestamp: 'timestamp',
  createdAt: 'createdAt'
};

exports.Prisma.WorkLocationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  latitude: 'latitude',
  longitude: 'longitude',
  radius: 'radius',
  address: 'address',
  isDefault: 'isDefault',
  isActive: 'isActive',
  companyId: 'companyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TimesheetScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  startDate: 'startDate',
  endDate: 'endDate',
  status: 'status',
  totalHours: 'totalHours',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkAssignmentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  projectId: 'projectId',
  taskId: 'taskId',
  companyId: 'companyId',
  assignedBy: 'assignedBy',
  startDate: 'startDate',
  endDate: 'endDate',
  status: 'status',
  priority: 'priority',
  estimatedHours: 'estimatedHours',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TaskSuggestionScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  userId: 'userId',
  projectId: 'projectId',
  companyId: 'companyId',
  status: 'status',
  reviewedBy: 'reviewedBy',
  reviewedAt: 'reviewedAt',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PayrollReportScalarFieldEnum = {
  id: 'id',
  name: 'name',
  companyId: 'companyId',
  startDate: 'startDate',
  endDate: 'endDate',
  status: 'status',
  totalAmount: 'totalAmount',
  currency: 'currency',
  generatedBy: 'generatedBy',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PayrollEntryScalarFieldEnum = {
  id: 'id',
  payrollReportId: 'payrollReportId',
  userId: 'userId',
  regularHours: 'regularHours',
  overtimeHours: 'overtimeHours',
  hourlyRate: 'hourlyRate',
  overtimeRate: 'overtimeRate',
  totalAmount: 'totalAmount',
  deductions: 'deductions',
  netAmount: 'netAmount',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ClientAccessScalarFieldEnum = {
  id: 'id',
  clientId: 'clientId',
  projectId: 'projectId',
  companyId: 'companyId',
  grantedBy: 'grantedBy',
  accessLevel: 'accessLevel',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TimeManagementSettingsScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  trackLocation: 'trackLocation',
  enforceLocationCompliance: 'enforceLocationCompliance',
  allowManualTimeEntry: 'allowManualTimeEntry',
  requireApproval: 'requireApproval',
  roundingInterval: 'roundingInterval',
  maxDailyHours: 'maxDailyHours',
  overtimeThreshold: 'overtimeThreshold',
  breakTime: 'breakTime',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TaskDependencyScalarFieldEnum = {
  id: 'id',
  parentTaskId: 'parentTaskId',
  dependentTaskId: 'dependentTaskId',
  type: 'type',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TaskAssignmentScalarFieldEnum = {
  id: 'id',
  taskId: 'taskId',
  userId: 'userId',
  role: 'role',
  assignedAt: 'assignedAt',
  completedAt: 'completedAt',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QualityCheckItemScalarFieldEnum = {
  id: 'id',
  checklistId: 'checklistId',
  description: 'description',
  status: 'status',
  notes: 'notes',
  order: 'order',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PhotoEvidenceScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  taskId: 'taskId',
  title: 'title',
  description: 'description',
  imageUrl: 'imageUrl',
  takenAt: 'takenAt',
  takenById: 'takenById',
  location: 'location',
  tags: 'tags',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProjectDocumentScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  title: 'title',
  description: 'description',
  fileUrl: 'fileUrl',
  fileType: 'fileType',
  fileSize: 'fileSize',
  uploadedById: 'uploadedById',
  category: 'category',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProjectTimelineEventScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  title: 'title',
  description: 'description',
  eventDate: 'eventDate',
  endDate: 'endDate',
  type: 'type',
  status: 'status',
  color: 'color',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EquipmentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  status: 'status',
  serialNumber: 'serialNumber',
  purchaseDate: 'purchaseDate',
  purchasePrice: 'purchasePrice',
  currentValue: 'currentValue',
  location: 'location',
  companyId: 'companyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EquipmentMaintenanceScalarFieldEnum = {
  id: 'id',
  equipmentId: 'equipmentId',
  maintenanceDate: 'maintenanceDate',
  description: 'description',
  cost: 'cost',
  performedById: 'performedById',
  status: 'status',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MaterialScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  unit: 'unit',
  unitPrice: 'unitPrice',
  quantity: 'quantity',
  minimumStock: 'minimumStock',
  location: 'location',
  companyId: 'companyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MaterialTransactionScalarFieldEnum = {
  id: 'id',
  materialId: 'materialId',
  type: 'type',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice',
  date: 'date',
  projectId: 'projectId',
  performedById: 'performedById',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ResourceAllocationScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  taskId: 'taskId',
  equipmentId: 'equipmentId',
  materialId: 'materialId',
  userId: 'userId',
  startDate: 'startDate',
  endDate: 'endDate',
  quantity: 'quantity',
  status: 'status',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductionReportScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  projectId: 'projectId',
  reportDate: 'reportDate',
  reportType: 'reportType',
  createdById: 'createdById',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductionMetricScalarFieldEnum = {
  id: 'id',
  reportId: 'reportId',
  name: 'name',
  value: 'value',
  unit: 'unit',
  target: 'target',
  category: 'category',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WaitlistEmailScalarFieldEnum = {
  id: 'id',
  email: 'email',
  confirmationToken: 'confirmationToken',
  tokenExpiresAt: 'tokenExpiresAt',
  isConfirmed: 'isConfirmed',
  confirmedAt: 'confirmedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CalendarEventScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  startDate: 'startDate',
  endDate: 'endDate',
  duration: 'duration',
  category: 'category',
  status: 'status',
  companyId: 'companyId',
  createdById: 'createdById',
  isRecurring: 'isRecurring',
  recurrenceRule: 'recurrenceRule',
  isAllDay: 'isAllDay',
  location: 'location',
  meetingUrl: 'meetingUrl',
  taskId: 'taskId',
  activityId: 'activityId',
  projectId: 'projectId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EventParticipantScalarFieldEnum = {
  id: 'id',
  eventId: 'eventId',
  userId: 'userId',
  status: 'status',
  response: 'response',
  invitedAt: 'invitedAt',
  respondedAt: 'respondedAt'
};

exports.Prisma.CalendarSettingsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  defaultView: 'defaultView',
  workingHours: 'workingHours',
  workingDays: 'workingDays',
  timezone: 'timezone',
  emailNotifications: 'emailNotifications',
  pushNotifications: 'pushNotifications',
  reminderMinutes: 'reminderMinutes',
  showWeekends: 'showWeekends',
  firstDayOfWeek: 'firstDayOfWeek',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EventReminderScalarFieldEnum = {
  id: 'id',
  eventId: 'eventId',
  userId: 'userId',
  reminderType: 'reminderType',
  minutesBefore: 'minutesBefore',
  isSent: 'isSent',
  sentAt: 'sentAt',
  createdAt: 'createdAt'
};

exports.Prisma.GpsDeviceScalarFieldEnum = {
  id: 'id',
  deviceId: 'deviceId',
  name: 'name',
  description: 'description',
  isActive: 'isActive',
  batteryLevel: 'batteryLevel',
  lastSeen: 'lastSeen',
  reportingInterval: 'reportingInterval',
  geofenceRadius: 'geofenceRadius',
  assetId: 'assetId',
  companyId: 'companyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.GpsLocationScalarFieldEnum = {
  id: 'id',
  latitude: 'latitude',
  longitude: 'longitude',
  altitude: 'altitude',
  accuracy: 'accuracy',
  speed: 'speed',
  heading: 'heading',
  timestamp: 'timestamp',
  deviceId: 'deviceId',
  batteryLevel: 'batteryLevel',
  signalStrength: 'signalStrength',
  createdAt: 'createdAt'
};

exports.Prisma.TrackableAssetScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  status: 'status',
  serialNumber: 'serialNumber',
  model: 'model',
  manufacturer: 'manufacturer',
  purchaseDate: 'purchaseDate',
  purchasePrice: 'purchasePrice',
  currentValue: 'currentValue',
  companyId: 'companyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TrackableAssetLocationScalarFieldEnum = {
  id: 'id',
  assetId: 'assetId',
  latitude: 'latitude',
  longitude: 'longitude',
  address: 'address',
  lastUpdate: 'lastUpdate',
  isInGeofence: 'isInGeofence',
  geofenceName: 'geofenceName'
};

exports.Prisma.TrackableAssetAssignmentScalarFieldEnum = {
  id: 'id',
  assetId: 'assetId',
  projectId: 'projectId',
  assignedAt: 'assignedAt',
  assignedBy: 'assignedBy',
  unassignedAt: 'unassignedAt',
  unassignedBy: 'unassignedBy',
  notes: 'notes'
};

exports.Prisma.WorkerLocationScalarFieldEnum = {
  id: 'id',
  workerId: 'workerId',
  latitude: 'latitude',
  longitude: 'longitude',
  accuracy: 'accuracy',
  timestamp: 'timestamp',
  projectId: 'projectId',
  taskId: 'taskId',
  timeEntryId: 'timeEntryId',
  isCompliant: 'isCompliant',
  workLocationId: 'workLocationId',
  createdAt: 'createdAt'
};

exports.Prisma.MapLayerScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  isDefault: 'isDefault',
  isActive: 'isActive',
  config: 'config',
  companyId: 'companyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanyJoinRequestScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyName: 'companyName',
  status: 'status',
  role: 'role',
  message: 'message',
  responseMessage: 'responseMessage',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  companyId: 'companyId',
  isRead: 'isRead'
};

exports.Prisma.CompanyInvitationScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  email: 'email',
  role: 'role',
  invitationToken: 'invitationToken',
  status: 'status',
  message: 'message',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  expiresAt: 'expiresAt'
};

exports.Prisma.DatabaseConnectionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  connectionString: 'connectionString',
  type: 'type',
  host: 'host',
  port: 'port',
  database: 'database',
  username: 'username',
  password: 'password',
  ssl: 'ssl',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DatabaseSettingScalarFieldEnum = {
  key: 'key',
  value: 'value'
};

exports.Prisma.RecruitmentFormScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  status: 'status',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  isPublic: 'isPublic',
  allowAnonymous: 'allowAnonymous',
  notifyEmails: 'notifyEmails',
  redirectUrl: 'redirectUrl',
  thankYouMessage: 'thankYouMessage',
  viewCount: 'viewCount',
  submissionCount: 'submissionCount',
  conversionRate: 'conversionRate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  publishedAt: 'publishedAt',
  expiresAt: 'expiresAt'
};

exports.Prisma.FormStepScalarFieldEnum = {
  id: 'id',
  formId: 'formId',
  title: 'title',
  description: 'description',
  order: 'order',
  isEnabled: 'isEnabled',
  translations: 'translations',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FormFieldScalarFieldEnum = {
  id: 'id',
  formId: 'formId',
  stepId: 'stepId',
  type: 'type',
  name: 'name',
  label: 'label',
  placeholder: 'placeholder',
  helpText: 'helpText',
  defaultValue: 'defaultValue',
  order: 'order',
  isRequired: 'isRequired',
  isEnabled: 'isEnabled',
  isHidden: 'isHidden',
  validations: 'validations',
  conditionalLogic: 'conditionalLogic',
  translations: 'translations',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FieldOptionScalarFieldEnum = {
  id: 'id',
  fieldId: 'fieldId',
  label: 'label',
  value: 'value',
  order: 'order',
  isDefault: 'isDefault',
  translations: 'translations',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FormSubmissionScalarFieldEnum = {
  id: 'id',
  formId: 'formId',
  data: 'data',
  submittedBy: 'submittedBy',
  submitterEmail: 'submitterEmail',
  submitterName: 'submitterName',
  submitterIp: 'submitterIp',
  status: 'status',
  reviewedBy: 'reviewedBy',
  reviewedAt: 'reviewedAt',
  notes: 'notes',
  workerId: 'workerId',
  referralCode: 'referralCode',
  source: 'source',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FieldResponseScalarFieldEnum = {
  id: 'id',
  submissionId: 'submissionId',
  fieldId: 'fieldId',
  value: 'value',
  fileUrl: 'fileUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkforceApplicationScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  status: 'status',
  referralCode: 'referralCode',
  referredBy: 'referredBy',
  fullName: 'fullName',
  email: 'email',
  phone: 'phone',
  dateOfBirth: 'dateOfBirth',
  gender: 'gender',
  nationality: 'nationality',
  address: 'address',
  preferredContact: 'preferredContact',
  currentJobTitle: 'currentJobTitle',
  currentEmployer: 'currentEmployer',
  yearsOfExperience: 'yearsOfExperience',
  category: 'category',
  role: 'role',
  proficiencyLevel: 'proficiencyLevel',
  additionalSkills: 'additionalSkills',
  educationLevel: 'educationLevel',
  fieldOfStudy: 'fieldOfStudy',
  portfolioLinks: 'portfolioLinks',
  availability: 'availability',
  willingToRelocate: 'willingToRelocate',
  salaryExpectations: 'salaryExpectations',
  preferredLocations: 'preferredLocations',
  specializedTools: 'specializedTools',
  safetyTraining: 'safetyTraining',
  strengths: 'strengths',
  areasForImprovement: 'areasForImprovement',
  hobbies: 'hobbies'
};

exports.Prisma.WorkforcePreviousJobScalarFieldEnum = {
  id: 'id',
  applicationId: 'applicationId',
  company: 'company',
  role: 'role',
  startDate: 'startDate',
  endDate: 'endDate',
  duties: 'duties'
};

exports.Prisma.WorkforceSecondarySpecialtyScalarFieldEnum = {
  id: 'id',
  applicationId: 'applicationId',
  specialty: 'specialty',
  proficiency: 'proficiency'
};

exports.Prisma.WorkforceCertificationScalarFieldEnum = {
  id: 'id',
  applicationId: 'applicationId',
  name: 'name',
  organization: 'organization',
  dateObtained: 'dateObtained',
  expirationDate: 'expirationDate'
};

exports.Prisma.WorkforceLicenseScalarFieldEnum = {
  id: 'id',
  applicationId: 'applicationId',
  name: 'name',
  organization: 'organization',
  dateObtained: 'dateObtained',
  expirationDate: 'expirationDate'
};

exports.Prisma.WorkforceProfessionalReferenceScalarFieldEnum = {
  id: 'id',
  applicationId: 'applicationId',
  name: 'name',
  relationship: 'relationship',
  contact: 'contact',
  company: 'company'
};

exports.Prisma.WorkforceEmergencyContactScalarFieldEnum = {
  id: 'id',
  applicationId: 'applicationId',
  name: 'name',
  relationship: 'relationship',
  contact: 'contact'
};

exports.Prisma.WorkforceLanguageScalarFieldEnum = {
  id: 'id',
  applicationId: 'applicationId',
  language: 'language',
  proficiency: 'proficiency'
};

exports.Prisma.WorkforceFormSettingsScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  referralEnabled: 'referralEnabled',
  referralText: 'referralText'
};

exports.Prisma.WorkforceFormStepScalarFieldEnum = {
  id: 'id',
  formSettingsId: 'formSettingsId',
  title: 'title',
  description: 'description',
  enabled: 'enabled',
  order: 'order'
};

exports.Prisma.WorkforceFormFieldScalarFieldEnum = {
  id: 'id',
  formSettingsId: 'formSettingsId',
  stepId: 'stepId',
  label: 'label',
  type: 'type',
  required: 'required',
  placeholder: 'placeholder',
  helpText: 'helpText',
  options: 'options',
  enabled: 'enabled',
  order: 'order'
};

exports.Prisma.JobPostingScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  requirements: 'requirements',
  responsibilities: 'responsibilities',
  location: 'location',
  employmentType: 'employmentType',
  salaryMin: 'salaryMin',
  salaryMax: 'salaryMax',
  currency: 'currency',
  department: 'department',
  experienceLevel: 'experienceLevel',
  status: 'status',
  isRemote: 'isRemote',
  benefits: 'benefits',
  skills: 'skills',
  companyId: 'companyId',
  createdBy: 'createdBy',
  publishedAt: 'publishedAt',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobApplicationScalarFieldEnum = {
  id: 'id',
  jobId: 'jobId',
  applicantId: 'applicantId',
  applicantName: 'applicantName',
  applicantEmail: 'applicantEmail',
  applicantPhone: 'applicantPhone',
  coverLetter: 'coverLetter',
  resumeUrl: 'resumeUrl',
  portfolioUrl: 'portfolioUrl',
  status: 'status',
  stage: 'stage',
  reviewedBy: 'reviewedBy',
  reviewedAt: 'reviewedAt',
  notes: 'notes',
  score: 'score',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InterviewScalarFieldEnum = {
  id: 'id',
  applicationId: 'applicationId',
  title: 'title',
  description: 'description',
  scheduledAt: 'scheduledAt',
  duration: 'duration',
  location: 'location',
  meetingUrl: 'meetingUrl',
  type: 'type',
  status: 'status',
  interviewerId: 'interviewerId',
  feedback: 'feedback',
  rating: 'rating',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AssessmentScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  type: 'type',
  questions: 'questions',
  timeLimit: 'timeLimit',
  passingScore: 'passingScore',
  isActive: 'isActive',
  companyId: 'companyId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AssessmentResultScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  applicantId: 'applicantId',
  answers: 'answers',
  score: 'score',
  passed: 'passed',
  timeSpent: 'timeSpent',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmailTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  subject: 'subject',
  body: 'body',
  type: 'type',
  isActive: 'isActive',
  companyId: 'companyId',
  variables: 'variables',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RecruitmentAnalyticsScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  totalJobs: 'totalJobs',
  activeJobs: 'activeJobs',
  totalApplications: 'totalApplications',
  pendingApplications: 'pendingApplications',
  shortlistedApplications: 'shortlistedApplications',
  interviewsScheduled: 'interviewsScheduled',
  hiredCandidates: 'hiredCandidates',
  avgTimeToHire: 'avgTimeToHire',
  avgTimeToInterview: 'avgTimeToInterview',
  applicationSources: 'applicationSources',
  periodStart: 'periodStart',
  periodEnd: 'periodEnd',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkerProfileScalarFieldEnum = {
  id: 'id',
  workerId: 'workerId',
  specialization: 'specialization',
  experienceYears: 'experienceYears',
  hourlyRate: 'hourlyRate',
  dailyRate: 'dailyRate',
  weeklyRate: 'weeklyRate',
  monthlyRate: 'monthlyRate',
  verificationStatus: 'verificationStatus',
  verifiedAt: 'verifiedAt',
  verifiedBy: 'verifiedBy',
  overallRating: 'overallRating',
  reliabilityScore: 'reliabilityScore',
  qualityScore: 'qualityScore',
  punctualityScore: 'punctualityScore',
  availableFrom: 'availableFrom',
  currentlyAvailable: 'currentlyAvailable',
  lastHealthCheck: 'lastHealthCheck',
  healthStatus: 'healthStatus',
  searchTags: 'searchTags',
  profileViews: 'profileViews',
  matchScore: 'matchScore',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobRequestScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  title: 'title',
  specialization: 'specialization',
  description: 'description',
  requiredSkills: 'requiredSkills',
  requiredCertifications: 'requiredCertifications',
  experienceLevel: 'experienceLevel',
  location: 'location',
  startDate: 'startDate',
  endDate: 'endDate',
  duration: 'duration',
  workingHours: 'workingHours',
  budget: 'budget',
  rateType: 'rateType',
  status: 'status',
  urgency: 'urgency',
  views: 'views',
  applicationsCount: 'applicationsCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PreferredWorkerScalarFieldEnum = {
  id: 'id',
  jobRequestId: 'jobRequestId',
  workerId: 'workerId',
  priority: 'priority',
  note: 'note',
  createdAt: 'createdAt'
};

exports.Prisma.JobMatchScalarFieldEnum = {
  id: 'id',
  workerProfileId: 'workerProfileId',
  jobRequestId: 'jobRequestId',
  matchScore: 'matchScore',
  matchReason: 'matchReason',
  status: 'status',
  workerInterested: 'workerInterested',
  companyInterested: 'companyInterested',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.IntermediationAssignmentScalarFieldEnum = {
  id: 'id',
  workerProfileId: 'workerProfileId',
  jobRequestId: 'jobRequestId',
  matchId: 'matchId',
  startDate: 'startDate',
  endDate: 'endDate',
  rate: 'rate',
  rateType: 'rateType',
  totalAmount: 'totalAmount',
  status: 'status',
  terminationReason: 'terminationReason',
  trialPeriod: 'trialPeriod',
  trialEndDate: 'trialEndDate',
  trialFeedback: 'trialFeedback',
  contractDocument: 'contractDocument',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.IntermediationTimeEntryScalarFieldEnum = {
  id: 'id',
  assignmentId: 'assignmentId',
  date: 'date',
  hoursWorked: 'hoursWorked',
  description: 'description',
  status: 'status',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkerReviewScalarFieldEnum = {
  id: 'id',
  workerProfileId: 'workerProfileId',
  assignmentId: 'assignmentId',
  overallRating: 'overallRating',
  qualityRating: 'qualityRating',
  reliabilityRating: 'reliabilityRating',
  punctualityRating: 'punctualityRating',
  comment: 'comment',
  reviewedBy: 'reviewedBy',
  reviewedAt: 'reviewedAt',
  isPublic: 'isPublic'
};

exports.Prisma.CompanyReviewScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  assignmentId: 'assignmentId',
  overallRating: 'overallRating',
  workEnvironmentRating: 'workEnvironmentRating',
  managementRating: 'managementRating',
  paymentPunctualityRating: 'paymentPunctualityRating',
  comment: 'comment',
  reviewedBy: 'reviewedBy',
  reviewedAt: 'reviewedAt',
  isPublic: 'isPublic'
};

exports.Prisma.SafetyTrainingScalarFieldEnum = {
  id: 'id',
  workerProfileId: 'workerProfileId',
  trainingType: 'trainingType',
  provider: 'provider',
  completionDate: 'completionDate',
  expiryDate: 'expiryDate',
  certificateUrl: 'certificateUrl',
  verified: 'verified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RentalCompanyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  contactPerson: 'contactPerson',
  email: 'email',
  phone: 'phone',
  address: 'address',
  website: 'website',
  partnershipLevel: 'partnershipLevel',
  commissionRate: 'commissionRate',
  contractStartDate: 'contractStartDate',
  contractEndDate: 'contractEndDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.Role = exports.$Enums.Role = {
  SUPERADMIN: 'SUPERADMIN',
  ADMIN: 'ADMIN',
  PROJECTLEADER: 'PROJECTLEADER',
  SALESMAN: 'SALESMAN',
  WORKER: 'WORKER',
  CLIENT: 'CLIENT',
  SUPPORTER: 'SUPPORTER'
};

exports.CompanyType = exports.$Enums.CompanyType = {
  CLIENT: 'CLIENT',
  SUPPLIER: 'SUPPLIER',
  PARTNER_AGENCY: 'PARTNER_AGENCY',
  INDIVIDUAL_CLIENT: 'INDIVIDUAL_CLIENT',
  COMPANY_CLIENT: 'COMPANY_CLIENT'
};

exports.CompanyStatus = exports.$Enums.CompanyStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED'
};

exports.RequestStatus = exports.$Enums.RequestStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  CANCELLED: 'CANCELLED'
};

exports.RiskLevel = exports.$Enums.RiskLevel = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

exports.ProjectStatus = exports.$Enums.ProjectStatus = {
  PLANNING: 'PLANNING',
  IN_PROGRESS: 'IN_PROGRESS',
  ON_HOLD: 'ON_HOLD',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.TaskStatus = exports.$Enums.TaskStatus = {
  TODO: 'TODO',
  IN_PROGRESS: 'IN_PROGRESS',
  REVIEW: 'REVIEW',
  COMPLETED: 'COMPLETED'
};

exports.Priority = exports.$Enums.Priority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT'
};

exports.DocumentType = exports.$Enums.DocumentType = {
  ID_CARD: 'ID_CARD',
  PASSPORT: 'PASSPORT',
  WORK_PERMIT: 'WORK_PERMIT',
  CERTIFICATION: 'CERTIFICATION',
  CONTRACT: 'CONTRACT',
  HEALTH_CHECK: 'HEALTH_CHECK',
  SPECIFICATION: 'SPECIFICATION',
  DRAWING: 'DRAWING',
  INSTRUCTION: 'INSTRUCTION',
  REPORT: 'REPORT',
  OTHER: 'OTHER'
};

exports.WorkerStatus = exports.$Enums.WorkerStatus = {
  AVAILABLE: 'AVAILABLE',
  EMPLOYED: 'EMPLOYED',
  ON_RENTAL: 'ON_RENTAL',
  INACTIVE: 'INACTIVE',
  BLACKLISTED: 'BLACKLISTED'
};

exports.Duration = exports.$Enums.Duration = {
  DAILY: 'DAILY',
  WEEKLY: 'WEEKLY',
  MONTHLY: 'MONTHLY',
  QUARTERLY: 'QUARTERLY',
  YEARLY: 'YEARLY'
};

exports.ContractStatus = exports.$Enums.ContractStatus = {
  DRAFT: 'DRAFT',
  ACTIVE: 'ACTIVE',
  SUSPENDED: 'SUSPENDED',
  TERMINATED: 'TERMINATED'
};

exports.RentalStatus = exports.$Enums.RentalStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.EmployeeStatus = exports.$Enums.EmployeeStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  ON_LEAVE: 'ON_LEAVE',
  TERMINATED: 'TERMINATED',
  SUSPENDED: 'SUSPENDED'
};

exports.EmploymentType = exports.$Enums.EmploymentType = {
  FULL_TIME: 'FULL_TIME',
  PART_TIME: 'PART_TIME',
  CONTRACT: 'CONTRACT',
  TEMPORARY: 'TEMPORARY',
  INTERN: 'INTERN'
};

exports.LeaveType = exports.$Enums.LeaveType = {
  VACATION: 'VACATION',
  SICK: 'SICK',
  PERSONAL: 'PERSONAL',
  MATERNITY: 'MATERNITY',
  PATERNITY: 'PATERNITY',
  BEREAVEMENT: 'BEREAVEMENT',
  UNPAID: 'UNPAID',
  OTHER: 'OTHER'
};

exports.LeaveStatus = exports.$Enums.LeaveStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  CANCELLED: 'CANCELLED'
};

exports.PayrollStatus = exports.$Enums.PayrollStatus = {
  DRAFT: 'DRAFT',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.BenefitType = exports.$Enums.BenefitType = {
  HEALTH_INSURANCE: 'HEALTH_INSURANCE',
  DENTAL_INSURANCE: 'DENTAL_INSURANCE',
  VISION_INSURANCE: 'VISION_INSURANCE',
  LIFE_INSURANCE: 'LIFE_INSURANCE',
  DISABILITY: 'DISABILITY',
  RETIREMENT: 'RETIREMENT',
  STOCK_OPTIONS: 'STOCK_OPTIONS',
  WELLNESS: 'WELLNESS',
  EDUCATION: 'EDUCATION',
  CHILDCARE: 'CHILDCARE',
  TRANSPORTATION: 'TRANSPORTATION',
  MEAL: 'MEAL',
  OTHER: 'OTHER'
};

exports.BonusType = exports.$Enums.BonusType = {
  PERFORMANCE: 'PERFORMANCE',
  ANNUAL: 'ANNUAL',
  REFERRAL: 'REFERRAL',
  SIGNING: 'SIGNING',
  RETENTION: 'RETENTION',
  PROJECT: 'PROJECT',
  HOLIDAY: 'HOLIDAY',
  SPOT: 'SPOT',
  OTHER: 'OTHER'
};

exports.BenefitStatus = exports.$Enums.BenefitStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  EXPIRED: 'EXPIRED'
};

exports.PerformanceRating = exports.$Enums.PerformanceRating = {
  EXCELLENT: 'EXCELLENT',
  GOOD: 'GOOD',
  SATISFACTORY: 'SATISFACTORY',
  NEEDS_IMPROVEMENT: 'NEEDS_IMPROVEMENT',
  POOR: 'POOR'
};

exports.TrainingType = exports.$Enums.TrainingType = {
  ONBOARDING: 'ONBOARDING',
  TECHNICAL: 'TECHNICAL',
  SOFT_SKILLS: 'SOFT_SKILLS',
  COMPLIANCE: 'COMPLIANCE',
  LEADERSHIP: 'LEADERSHIP',
  SAFETY: 'SAFETY',
  PRODUCT: 'PRODUCT',
  CERTIFICATION: 'CERTIFICATION',
  OTHER: 'OTHER'
};

exports.TrainingStatus = exports.$Enums.TrainingStatus = {
  SCHEDULED: 'SCHEDULED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.DocumentCategory = exports.$Enums.DocumentCategory = {
  PERSONAL: 'PERSONAL',
  EMPLOYMENT: 'EMPLOYMENT',
  PAYROLL: 'PAYROLL',
  BENEFITS: 'BENEFITS',
  PERFORMANCE: 'PERFORMANCE',
  TRAINING: 'TRAINING',
  LEGAL: 'LEGAL',
  OTHER: 'OTHER'
};

exports.RuleType = exports.$Enums.RuleType = {
  BUDGET_CONSTRAINT: 'BUDGET_CONSTRAINT',
  SKILL_REQUIREMENT: 'SKILL_REQUIREMENT',
  CERTIFICATION_REQUIREMENT: 'CERTIFICATION_REQUIREMENT',
  WORKER_MATCHING: 'WORKER_MATCHING',
  RISK_ASSESSMENT: 'RISK_ASSESSMENT',
  COMPLIANCE: 'COMPLIANCE'
};

exports.RuleSeverity = exports.$Enums.RuleSeverity = {
  INFO: 'INFO',
  WARNING: 'WARNING',
  ERROR: 'ERROR',
  CRITICAL: 'CRITICAL'
};

exports.AccountType = exports.$Enums.AccountType = {
  ASSET: 'ASSET',
  LIABILITY: 'LIABILITY',
  EQUITY: 'EQUITY',
  REVENUE: 'REVENUE',
  EXPENSE: 'EXPENSE'
};

exports.EntryStatus = exports.$Enums.EntryStatus = {
  DRAFT: 'DRAFT',
  POSTED: 'POSTED',
  VOIDED: 'VOIDED'
};

exports.PaymentMethod = exports.$Enums.PaymentMethod = {
  BANK: 'BANK',
  CASH: 'CASH',
  CRYPTO: 'CRYPTO',
  CARD: 'CARD',
  BANK_TRANSFER: 'BANK_TRANSFER',
  CREDIT_CARD: 'CREDIT_CARD',
  CHECK: 'CHECK',
  OTHER: 'OTHER'
};

exports.IncomeSource = exports.$Enums.IncomeSource = {
  SALARY: 'SALARY',
  DIVIDENDS: 'DIVIDENDS',
  SOCIAL_BENEFITS: 'SOCIAL_BENEFITS',
  LOAN: 'LOAN',
  GIFT: 'GIFT',
  FREELANCE: 'FREELANCE',
  BUSINESS: 'BUSINESS',
  OTHER: 'OTHER'
};

exports.IntervalType = exports.$Enums.IntervalType = {
  ONCE: 'ONCE',
  MULTIPLE: 'MULTIPLE',
  RECURRING: 'RECURRING'
};

exports.ExpenseSource = exports.$Enums.ExpenseSource = {
  RENT: 'RENT',
  GROCERY: 'GROCERY',
  UTILITIES: 'UTILITIES',
  TRANSPORT: 'TRANSPORT',
  SUBSCRIPTION: 'SUBSCRIPTION',
  LOAN_PAYMENT: 'LOAN_PAYMENT',
  ENTERTAINMENT: 'ENTERTAINMENT',
  MEDICAL: 'MEDICAL',
  EDUCATION: 'EDUCATION',
  TRAINING: 'TRAINING',
  ALIMONY: 'ALIMONY',
  OTHER: 'OTHER'
};

exports.AssetMethod = exports.$Enums.AssetMethod = {
  BANK: 'BANK',
  CRYPTO: 'CRYPTO',
  CASH: 'CASH',
  DEBTORS: 'DEBTORS',
  SAVINGS: 'SAVINGS',
  STOCKS: 'STOCKS',
  BONDS: 'BONDS',
  OTHER: 'OTHER'
};

exports.BudgetType = exports.$Enums.BudgetType = {
  PERSONAL: 'PERSONAL',
  COMPANY: 'COMPANY'
};

exports.TransactionType = exports.$Enums.TransactionType = {
  INCOME: 'INCOME',
  EXPENSE: 'EXPENSE'
};

exports.TransactionStatus = exports.$Enums.TransactionStatus = {
  COMPLETED: 'COMPLETED',
  PENDING: 'PENDING',
  FAILED: 'FAILED'
};

exports.GoalType = exports.$Enums.GoalType = {
  SAVING: 'SAVING',
  SPENDING: 'SPENDING'
};

exports.GoalStatus = exports.$Enums.GoalStatus = {
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED'
};

exports.EmailProvider = exports.$Enums.EmailProvider = {
  GMAIL: 'GMAIL',
  OUTLOOK: 'OUTLOOK',
  ZOHO: 'ZOHO',
  CUSTOM: 'CUSTOM'
};

exports.EmailStatus = exports.$Enums.EmailStatus = {
  READ: 'READ',
  UNREAD: 'UNREAD',
  FLAGGED: 'FLAGGED',
  DELETED: 'DELETED'
};

exports.EmailPriority = exports.$Enums.EmailPriority = {
  HIGH: 'HIGH',
  NORMAL: 'NORMAL',
  LOW: 'LOW'
};

exports.ChatConversationType = exports.$Enums.ChatConversationType = {
  DIRECT: 'DIRECT',
  GROUP: 'GROUP'
};

exports.ChatMessageStatus = exports.$Enums.ChatMessageStatus = {
  SENT: 'SENT',
  DELIVERED: 'DELIVERED',
  READ: 'READ',
  FAILED: 'FAILED'
};

exports.EndpointMethod = exports.$Enums.EndpointMethod = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE'
};

exports.EndpointStatus = exports.$Enums.EndpointStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  DEPRECATED: 'DEPRECATED'
};

exports.SubscriptionPlanType = exports.$Enums.SubscriptionPlanType = {
  FREE_TRIAL: 'FREE_TRIAL',
  STARTER: 'STARTER',
  PREMIUM: 'PREMIUM'
};

exports.SubscriptionStatus = exports.$Enums.SubscriptionStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  TRIAL: 'TRIAL',
  EXPIRED: 'EXPIRED',
  CANCELLED: 'CANCELLED',
  PENDING_PAYMENT: 'PENDING_PAYMENT'
};

exports.BillingCycle = exports.$Enums.BillingCycle = {
  MONTHLY: 'MONTHLY',
  YEARLY: 'YEARLY'
};

exports.LeadStatus = exports.$Enums.LeadStatus = {
  NEW: 'NEW',
  CONTACTED: 'CONTACTED',
  QUALIFIED: 'QUALIFIED',
  UNQUALIFIED: 'UNQUALIFIED',
  CONVERTED: 'CONVERTED',
  LOST: 'LOST'
};

exports.LeadSource = exports.$Enums.LeadSource = {
  WEBSITE: 'WEBSITE',
  REFERRAL: 'REFERRAL',
  SOCIAL_MEDIA: 'SOCIAL_MEDIA',
  EMAIL_CAMPAIGN: 'EMAIL_CAMPAIGN',
  COLD_CALL: 'COLD_CALL',
  EVENT: 'EVENT',
  PARTNER: 'PARTNER',
  ADVERTISEMENT: 'ADVERTISEMENT',
  OTHER: 'OTHER'
};

exports.DealStage = exports.$Enums.DealStage = {
  PROSPECTING: 'PROSPECTING',
  QUALIFICATION: 'QUALIFICATION',
  NEEDS_ANALYSIS: 'NEEDS_ANALYSIS',
  VALUE_PROPOSITION: 'VALUE_PROPOSITION',
  PROPOSAL: 'PROPOSAL',
  NEGOTIATION: 'NEGOTIATION',
  CLOSED_WON: 'CLOSED_WON',
  CLOSED_LOST: 'CLOSED_LOST'
};

exports.DealType = exports.$Enums.DealType = {
  NEW_BUSINESS: 'NEW_BUSINESS',
  EXISTING_BUSINESS: 'EXISTING_BUSINESS',
  RENEWAL: 'RENEWAL',
  UPSELL: 'UPSELL',
  CROSS_SELL: 'CROSS_SELL'
};

exports.ActivityType = exports.$Enums.ActivityType = {
  CALL: 'CALL',
  EMAIL: 'EMAIL',
  MEETING: 'MEETING',
  TASK: 'TASK',
  NOTE: 'NOTE',
  FOLLOW_UP: 'FOLLOW_UP',
  DEMO: 'DEMO',
  PRESENTATION: 'PRESENTATION'
};

exports.ActivityStatus = exports.$Enums.ActivityStatus = {
  PLANNED: 'PLANNED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.CampaignType = exports.$Enums.CampaignType = {
  EMAIL: 'EMAIL',
  SOCIAL_MEDIA: 'SOCIAL_MEDIA',
  EVENT: 'EVENT',
  WEBINAR: 'WEBINAR',
  DIRECT_MAIL: 'DIRECT_MAIL',
  TELEMARKETING: 'TELEMARKETING',
  CONTENT: 'CONTENT',
  REFERRAL_PROGRAM: 'REFERRAL_PROGRAM',
  PARTNER: 'PARTNER'
};

exports.CampaignStatus = exports.$Enums.CampaignStatus = {
  DRAFT: 'DRAFT',
  SCHEDULED: 'SCHEDULED',
  ACTIVE: 'ACTIVE',
  PAUSED: 'PAUSED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.ProductCategory = exports.$Enums.ProductCategory = {
  PHYSICAL: 'PHYSICAL',
  DIGITAL: 'DIGITAL',
  SERVICE: 'SERVICE',
  SUBSCRIPTION: 'SUBSCRIPTION'
};

exports.QuotationStatus = exports.$Enums.QuotationStatus = {
  DRAFT: 'DRAFT',
  SENT: 'SENT',
  VIEWED: 'VIEWED',
  ACCEPTED: 'ACCEPTED',
  REJECTED: 'REJECTED',
  EXPIRED: 'EXPIRED'
};

exports.TimeEntryStatus = exports.$Enums.TimeEntryStatus = {
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  PAUSED: 'PAUSED',
  REJECTED: 'REJECTED'
};

exports.TimesheetStatus = exports.$Enums.TimesheetStatus = {
  DRAFT: 'DRAFT',
  SUBMITTED: 'SUBMITTED',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.AssignmentStatus = exports.$Enums.AssignmentStatus = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  REJECTED: 'REJECTED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.SuggestionStatus = exports.$Enums.SuggestionStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.ClientAccessLevel = exports.$Enums.ClientAccessLevel = {
  VIEW_ONLY: 'VIEW_ONLY',
  DETAILED: 'DETAILED',
  FULL_ACCESS: 'FULL_ACCESS'
};

exports.EventCategory = exports.$Enums.EventCategory = {
  GENERAL: 'GENERAL',
  MEETING: 'MEETING',
  TASK: 'TASK',
  PROJECT: 'PROJECT',
  SALES: 'SALES',
  HR: 'HR',
  PERSONAL: 'PERSONAL',
  COMPANY: 'COMPANY',
  CLIENT: 'CLIENT',
  DEADLINE: 'DEADLINE'
};

exports.EventStatus = exports.$Enums.EventStatus = {
  SCHEDULED: 'SCHEDULED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  POSTPONED: 'POSTPONED'
};

exports.ParticipantStatus = exports.$Enums.ParticipantStatus = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  DECLINED: 'DECLINED',
  TENTATIVE: 'TENTATIVE',
  NO_RESPONSE: 'NO_RESPONSE'
};

exports.ReminderType = exports.$Enums.ReminderType = {
  EMAIL: 'EMAIL',
  PUSH: 'PUSH',
  SMS: 'SMS',
  POPUP: 'POPUP'
};

exports.TrackableAssetType = exports.$Enums.TrackableAssetType = {
  VEHICLE: 'VEHICLE',
  EQUIPMENT: 'EQUIPMENT',
  TOOL: 'TOOL',
  MACHINERY: 'MACHINERY',
  DEVICE: 'DEVICE',
  OTHER: 'OTHER'
};

exports.TrackableAssetStatus = exports.$Enums.TrackableAssetStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  MAINTENANCE: 'MAINTENANCE',
  REPAIR: 'REPAIR',
  LOST: 'LOST',
  STOLEN: 'STOLEN',
  DISPOSED: 'DISPOSED'
};

exports.MapLayerType = exports.$Enums.MapLayerType = {
  PROJECTS: 'PROJECTS',
  WORKERS: 'WORKERS',
  ASSETS: 'ASSETS',
  WORK_LOCATIONS: 'WORK_LOCATIONS',
  GEOFENCES: 'GEOFENCES',
  CUSTOM: 'CUSTOM'
};

exports.FormStatus = exports.$Enums.FormStatus = {
  draft: 'draft',
  published: 'published',
  archived: 'archived',
  expired: 'expired'
};

exports.FieldType = exports.$Enums.FieldType = {
  text: 'text',
  textarea: 'textarea',
  email: 'email',
  phone: 'phone',
  number: 'number',
  date: 'date',
  time: 'time',
  datetime: 'datetime',
  select: 'select',
  multiselect: 'multiselect',
  radio: 'radio',
  checkbox: 'checkbox',
  file: 'file',
  image: 'image',
  address: 'address',
  name: 'name',
  url: 'url',
  password: 'password',
  hidden: 'hidden',
  heading: 'heading',
  paragraph: 'paragraph',
  divider: 'divider',
  signature: 'signature',
  rating: 'rating',
  slider: 'slider'
};

exports.SubmissionStatus = exports.$Enums.SubmissionStatus = {
  new: 'new',
  reviewed: 'reviewed',
  shortlisted: 'shortlisted',
  contacted: 'contacted',
  interviewed: 'interviewed',
  hired: 'hired',
  rejected: 'rejected',
  archived: 'archived'
};

exports.WorkforceApplicationStatus = exports.$Enums.WorkforceApplicationStatus = {
  new: 'new',
  contacted: 'contacted',
  reviewed: 'reviewed',
  hired: 'hired',
  rejected: 'rejected'
};

exports.ExperienceLevel = exports.$Enums.ExperienceLevel = {
  entry: 'entry',
  intermediate: 'intermediate',
  expert: 'expert',
  master: 'master'
};

exports.JobStatus = exports.$Enums.JobStatus = {
  DRAFT: 'DRAFT',
  ACTIVE: 'ACTIVE',
  PAUSED: 'PAUSED',
  CLOSED: 'CLOSED',
  EXPIRED: 'EXPIRED'
};

exports.ApplicationStatus = exports.$Enums.ApplicationStatus = {
  PENDING: 'PENDING',
  REVIEWED: 'REVIEWED',
  SHORTLISTED: 'SHORTLISTED',
  REJECTED: 'REJECTED',
  HIRED: 'HIRED'
};

exports.ApplicationStage = exports.$Enums.ApplicationStage = {
  APPLIED: 'APPLIED',
  SCREENING: 'SCREENING',
  INTERVIEW: 'INTERVIEW',
  ASSESSMENT: 'ASSESSMENT',
  REFERENCE_CHECK: 'REFERENCE_CHECK',
  OFFER: 'OFFER',
  HIRED: 'HIRED',
  REJECTED: 'REJECTED'
};

exports.InterviewType = exports.$Enums.InterviewType = {
  PHONE: 'PHONE',
  VIDEO: 'VIDEO',
  IN_PERSON: 'IN_PERSON',
  TECHNICAL: 'TECHNICAL',
  PANEL: 'PANEL'
};

exports.InterviewStatus = exports.$Enums.InterviewStatus = {
  SCHEDULED: 'SCHEDULED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  RESCHEDULED: 'RESCHEDULED'
};

exports.AssessmentType = exports.$Enums.AssessmentType = {
  TECHNICAL: 'TECHNICAL',
  BEHAVIORAL: 'BEHAVIORAL',
  COGNITIVE: 'COGNITIVE',
  PERSONALITY: 'PERSONALITY',
  SKILLS: 'SKILLS'
};

exports.EmailTemplateType = exports.$Enums.EmailTemplateType = {
  APPLICATION_RECEIVED: 'APPLICATION_RECEIVED',
  APPLICATION_REJECTED: 'APPLICATION_REJECTED',
  INTERVIEW_INVITATION: 'INTERVIEW_INVITATION',
  INTERVIEW_REMINDER: 'INTERVIEW_REMINDER',
  OFFER_LETTER: 'OFFER_LETTER',
  WELCOME_MESSAGE: 'WELCOME_MESSAGE'
};

exports.VerificationStatus = exports.$Enums.VerificationStatus = {
  pending: 'pending',
  inProgress: 'inProgress',
  verified: 'verified',
  rejected: 'rejected'
};

exports.HealthStatus = exports.$Enums.HealthStatus = {
  passed: 'passed',
  conditionalPass: 'conditionalPass',
  failed: 'failed',
  expired: 'expired'
};

exports.WorkingHoursType = exports.$Enums.WorkingHoursType = {
  fullTime: 'fullTime',
  partTime: 'partTime',
  flexible: 'flexible',
  shifts: 'shifts'
};

exports.RateType = exports.$Enums.RateType = {
  hourly: 'hourly',
  daily: 'daily',
  weekly: 'weekly',
  monthly: 'monthly',
  fixed: 'fixed'
};

exports.JobRequestStatus = exports.$Enums.JobRequestStatus = {
  draft: 'draft',
  open: 'open',
  inProgress: 'inProgress',
  filled: 'filled',
  completed: 'completed',
  cancelled: 'cancelled'
};

exports.UrgencyLevel = exports.$Enums.UrgencyLevel = {
  low: 'low',
  normal: 'normal',
  high: 'high',
  critical: 'critical'
};

exports.MatchStatus = exports.$Enums.MatchStatus = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  expired: 'expired'
};

exports.WorkerAssignmentStatus = exports.$Enums.WorkerAssignmentStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  TERMINATED: 'TERMINATED',
  EXTENDED: 'EXTENDED'
};

exports.TimeEntryApprovalStatus = exports.$Enums.TimeEntryApprovalStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  MODIFIED: 'MODIFIED'
};

exports.PartnershipLevel = exports.$Enums.PartnershipLevel = {
  standard: 'standard',
  premium: 'premium',
  exclusive: 'exclusive'
};

exports.Prisma.ModelName = {
  User: 'User',
  SessionLog: 'SessionLog',
  UserRole: 'UserRole',
  UserCompany: 'UserCompany',
  Company: 'Company',
  WorkforceNeed: 'WorkforceNeed',
  FinancialHealth: 'FinancialHealth',
  ClientRelationship: 'ClientRelationship',
  SupplierRelationship: 'SupplierRelationship',
  CompanyTransition: 'CompanyTransition',
  ProjectProduct: 'ProjectProduct',
  Project: 'Project',
  Task: 'Task',
  QualityChecklist: 'QualityChecklist',
  QualityStep: 'QualityStep',
  Photo: 'Photo',
  Document: 'Document',
  Stage: 'Stage',
  Worker: 'Worker',
  LaborRequest: 'LaborRequest',
  Contract: 'Contract',
  Skill: 'Skill',
  Certification: 'Certification',
  WorkerRental: 'WorkerRental',
  WorkerDocument: 'WorkerDocument',
  WorkerAvailability: 'WorkerAvailability',
  Feedback: 'Feedback',
  HealthCheck: 'HealthCheck',
  SalaryInfo: 'SalaryInfo',
  TimeTrackingLog: 'TimeTrackingLog',
  Employee: 'Employee',
  Department: 'Department',
  Position: 'Position',
  LeaveRequest: 'LeaveRequest',
  LeaveBalance: 'LeaveBalance',
  Attendance: 'Attendance',
  Shift: 'Shift',
  Payroll: 'Payroll',
  EmployeeBenefitBasic: 'EmployeeBenefitBasic',
  BonusBasic: 'BonusBasic',
  TrainingBasic: 'TrainingBasic',
  CareerPathBasic: 'CareerPathBasic',
  Benefit: 'Benefit',
  EmployeeBenefit: 'EmployeeBenefit',
  Bonus: 'Bonus',
  PerformanceReview: 'PerformanceReview',
  Training: 'Training',
  EmployeeTraining: 'EmployeeTraining',
  CareerPath: 'CareerPath',
  HrDocument: 'HrDocument',
  EmployeeDocument: 'EmployeeDocument',
  BusinessRule: 'BusinessRule',
  BusinessStrategy: 'BusinessStrategy',
  AiDecisionLog: 'AiDecisionLog',
  SystemIntegration: 'SystemIntegration',
  Contact: 'Contact',
  Agreement: 'Agreement',
  RiskAssessment: 'RiskAssessment',
  PageAccessRight: 'PageAccessRight',
  Account: 'Account',
  JournalEntry: 'JournalEntry',
  Income: 'Income',
  Expense: 'Expense',
  Asset: 'Asset',
  Budget: 'Budget',
  Transaction: 'Transaction',
  BudgetGoal: 'BudgetGoal',
  EmailAccount: 'EmailAccount',
  EmailMessage: 'EmailMessage',
  EmailAttachment: 'EmailAttachment',
  EmailFolder: 'EmailFolder',
  ChatConversation: 'ChatConversation',
  ChatParticipant: 'ChatParticipant',
  ChatMessage: 'ChatMessage',
  CommunicationEndpoint: 'CommunicationEndpoint',
  EndpointRequest: 'EndpointRequest',
  SubscriptionPackage: 'SubscriptionPackage',
  LegacySubscription: 'LegacySubscription',
  SubscriptionPlan: 'SubscriptionPlan',
  Subscription: 'Subscription',
  Payment: 'Payment',
  Lead: 'Lead',
  LeadTag: 'LeadTag',
  LeadCustomField: 'LeadCustomField',
  Deal: 'Deal',
  DealProduct: 'DealProduct',
  DealNote: 'DealNote',
  Activity: 'Activity',
  Campaign: 'Campaign',
  CampaignLead: 'CampaignLead',
  CampaignMetric: 'CampaignMetric',
  Product: 'Product',
  Quotation: 'Quotation',
  QuotationItem: 'QuotationItem',
  SalesGoal: 'SalesGoal',
  SalesPipelineStage: 'SalesPipelineStage',
  TimeEntry: 'TimeEntry',
  Location: 'Location',
  WorkLocation: 'WorkLocation',
  Timesheet: 'Timesheet',
  WorkAssignment: 'WorkAssignment',
  TaskSuggestion: 'TaskSuggestion',
  PayrollReport: 'PayrollReport',
  PayrollEntry: 'PayrollEntry',
  ClientAccess: 'ClientAccess',
  TimeManagementSettings: 'TimeManagementSettings',
  TaskDependency: 'TaskDependency',
  TaskAssignment: 'TaskAssignment',
  QualityCheckItem: 'QualityCheckItem',
  PhotoEvidence: 'PhotoEvidence',
  ProjectDocument: 'ProjectDocument',
  ProjectTimelineEvent: 'ProjectTimelineEvent',
  Equipment: 'Equipment',
  EquipmentMaintenance: 'EquipmentMaintenance',
  Material: 'Material',
  MaterialTransaction: 'MaterialTransaction',
  ResourceAllocation: 'ResourceAllocation',
  ProductionReport: 'ProductionReport',
  ProductionMetric: 'ProductionMetric',
  WaitlistEmail: 'WaitlistEmail',
  CalendarEvent: 'CalendarEvent',
  EventParticipant: 'EventParticipant',
  CalendarSettings: 'CalendarSettings',
  EventReminder: 'EventReminder',
  GpsDevice: 'GpsDevice',
  GpsLocation: 'GpsLocation',
  TrackableAsset: 'TrackableAsset',
  TrackableAssetLocation: 'TrackableAssetLocation',
  TrackableAssetAssignment: 'TrackableAssetAssignment',
  WorkerLocation: 'WorkerLocation',
  MapLayer: 'MapLayer',
  CompanyJoinRequest: 'CompanyJoinRequest',
  CompanyInvitation: 'CompanyInvitation',
  DatabaseConnection: 'DatabaseConnection',
  DatabaseSetting: 'DatabaseSetting',
  RecruitmentForm: 'RecruitmentForm',
  FormStep: 'FormStep',
  FormField: 'FormField',
  FieldOption: 'FieldOption',
  FormSubmission: 'FormSubmission',
  FieldResponse: 'FieldResponse',
  WorkforceApplication: 'WorkforceApplication',
  WorkforcePreviousJob: 'WorkforcePreviousJob',
  WorkforceSecondarySpecialty: 'WorkforceSecondarySpecialty',
  WorkforceCertification: 'WorkforceCertification',
  WorkforceLicense: 'WorkforceLicense',
  WorkforceProfessionalReference: 'WorkforceProfessionalReference',
  WorkforceEmergencyContact: 'WorkforceEmergencyContact',
  WorkforceLanguage: 'WorkforceLanguage',
  WorkforceFormSettings: 'WorkforceFormSettings',
  WorkforceFormStep: 'WorkforceFormStep',
  WorkforceFormField: 'WorkforceFormField',
  JobPosting: 'JobPosting',
  JobApplication: 'JobApplication',
  Interview: 'Interview',
  Assessment: 'Assessment',
  AssessmentResult: 'AssessmentResult',
  EmailTemplate: 'EmailTemplate',
  RecruitmentAnalytics: 'RecruitmentAnalytics',
  WorkerProfile: 'WorkerProfile',
  JobRequest: 'JobRequest',
  PreferredWorker: 'PreferredWorker',
  JobMatch: 'JobMatch',
  IntermediationAssignment: 'IntermediationAssignment',
  IntermediationTimeEntry: 'IntermediationTimeEntry',
  WorkerReview: 'WorkerReview',
  CompanyReview: 'CompanyReview',
  SafetyTraining: 'SafetyTraining',
  RentalCompany: 'RentalCompany'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
