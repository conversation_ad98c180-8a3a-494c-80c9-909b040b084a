<!--  client/.app/app/pages/map/index.vue -->

<script setup lang="ts">
import type {
  GeoJSONSource,
  LngLatLike,
  Map,
  MapOptions,
  Popup,
  TargetFeature,
} from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import { useAuthStore } from "../../../stores/useAuthStore";

type MapboxGL = typeof import("mapbox-gl").default;

const { t } = useI18n();

definePageMeta({
  title: "Map locations",
});

// You must set the NUXT_PUBLIC_MAPBOX_TOKEN inside .env file
const options: Partial<MapOptions> = {
  center: [24.7536, 59.437], // Tallinn, Estonia
  zoom: 12,
  hash: true,
  antialias: true,
  projection: "globe",
  config: {
    basemap: {
      font: "Inter",
      showPointOfInterestLabels: false,
      showPedestrianRoads: false,
      showTransitLabels: false,
    },
  },
};

let mapboxgl: MapboxGL;

const mode = ref<"base" | "navigation" | "satellite">("base");

const isLoading = ref(true);
const hasError = ref(false);
const config = useRuntimeConfig();
const colorMode = useColorMode();
const mapElement = useTemplateRef<HTMLElement>("mapElement");
const popupElement = useTemplateRef<HTMLElement>("popupElement");
const selectedLocation = shallowRef<TargetFeature>();
const map = shallowRef<Map>();
const popup = shallowRef<Popup>();

// Layer visibility state
const layerVisibility = ref({
  projects: true,
  workers: true,
  rental_workers: true,
  assets: true,
  work_locations: true,
});

// API composable
const api = useApi();

// Auth store
const authStore = useAuthStore();

// Map data state
const mapData = ref<any>(null);
const status = ref("idle");

// Fetch map data function
async function fetchMapData() {
  status.value = "pending";
  try {
    // Check authentication
    console.log("Auth state:", {
      isLoggedIn: authStore.isLoggedIn,
      userId: authStore.userId,
      email: authStore.email,
      roles: authStore.roles,
      token: authStore.token,
    });

    if (!authStore.isLoggedIn) {
      console.warn("User not authenticated, skipping map data fetch");
      status.value = "error";
      mapData.value = {
        type: "FeatureCollection",
        features: [],
      };
      return;
    }

    const layers = Object.entries(layerVisibility.value)
      .filter(([_, visible]) => visible)
      .map(([layer]) => layer)
      .join(",");

    console.log("Fetching map data with layers:", layers);
    const response = await api.get(`/map/data?layers=${layers}`);
    console.log("Map data response:", response);
    mapData.value = response;
    status.value = "success";
  } catch (error) {
    console.error("Error fetching map data:", error);
    status.value = "error";
    mapData.value = {
      type: "FeatureCollection",
      features: [],
    };
  }
}

// Refresh function
const refresh = fetchMapData;

// Debug Mapbox token
console.log("Mapbox token check:", {
  token: config.public.mapboxToken,
  hasToken: !!config.public.mapboxToken,
  tokenLength: config.public.mapboxToken?.length || 0,
});

if (import.meta.dev && !config.public.mapboxToken) {
  console.warn(
    "NUXT_PUBLIC_MAPBOX_TOKEN environment variable is not defined, mapbox features are disabled"
  );
}

onMounted(async () => {
  // Ensure auth store is hydrated
  authStore.hydrateAuth();
  await fetchMapData();

  // Wait for next tick to ensure DOM is fully rendered
  await nextTick();

  // Add a small delay to ensure container is properly sized
  setTimeout(() => {
    initMapbox();
  }, 100);
});

onBeforeUnmount(() => {
  map.value?.remove();
  map.value = undefined;
});

watch(selectedLocation, updatePopup, { flush: "post" });
watch(selectedLocation, focusFeatureCard);
watch(mapData, loadMapDataSource);
watch(colorMode, updateMapStyle);
watch(
  layerVisibility,
  () => {
    fetchMapData();
  },
  { deep: true }
);

async function initMapbox() {
  try {
    if (!config.public.mapboxToken) {
      hasError.value = true;
      return;
    }
    if (!mapElement.value) {
      return;
    }

    // Wait for the container to be properly sized
    await nextTick();

    // Check if container has dimensions
    const rect = mapElement.value.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      console.warn("Map container has no dimensions, retrying...");
      setTimeout(initMapbox, 100);
      return;
    }

    hasError.value = false;
    isLoading.value = true;

    mapboxgl = await import("mapbox-gl").then((m) => m.default);
    mapboxgl.accessToken = config.public.mapboxToken;

    map.value = new mapboxgl.Map({
      ...options,
      container: mapElement.value,
      style: getMapStyle(),
    });
    map.value.on("style.load", waitStyleLoaded);
    map.value.on("error", (event) => {
      console.error("Mapbox error:", event);
      hasError.value = true;
    });
  } catch (error) {
    console.error("Error initializing Mapbox:", error);
    hasError.value = true;
  } finally {
    setTimeout(() => {
      isLoading.value = false;
    }, 100);
  }
}

async function waitStyleLoaded() {
  if (!map.value) {
    return;
  }

  loadMapDataSource();
  loadTerrain();

  if (!map.value.isStyleLoaded()) {
    await map.value.once("idle");
  }

  loadMapLayers();

  if (selectedLocation.value) {
    map.value?.setFeatureState(selectedLocation.value, { selected: true });
  }
}

function getMapStyle() {
  switch (mode.value) {
    case "base":
      return colorMode.value === "dark"
        ? "mapbox://styles/mapbox/dark-v11"
        : "mapbox://styles/mapbox/light-v11";
    case "navigation":
      return colorMode.value === "dark"
        ? "mapbox://styles/mapbox/navigation-night-v1"
        : "mapbox://styles/mapbox/navigation-day-v1";
    case "satellite":
      return "mapbox://styles/mapbox/standard-satellite";
  }
}

function updateMapStyle() {
  if (!map.value) {
    return;
  }

  const style = getMapStyle();
  if (map.value.style.globalId !== style) {
    map.value.setStyle(style);
    waitStyleLoaded();
  }

  if (mode.value === "satellite") {
    if (colorMode.value === "dark") {
      map.value.setConfigProperty("basemap", "lightPreset", "night");
    } else {
      map.value.setConfigProperty("basemap", "lightPreset", "day");
    }
  }
}

function loadMapLayers() {
  if (!map.value) {
    return;
  }
  if (!map.value.getSource("mapData")) {
    map.value.addSource("mapData", {
      type: "geojson",
      promoteId: "id",
    });
  }

  const layers = map.value?.getStyle()?.layers;
  const mapLayer = layers?.find((layer) => layer.id === "mapData");

  if (mapLayer?.id) {
    map.value?.removeLayer(mapLayer.id);
  }

  // Add different layers for different feature types
  map.value.addLayer({
    id: "mapData",
    type: "circle",
    source: "mapData",
    paint: {
      "circle-radius": [
        "case",
        ["==", ["get", "type"], "project"],
        12,
        ["==", ["get", "type"], "worker"],
        8,
        ["==", ["get", "type"], "asset"],
        10,
        ["==", ["get", "type"], "work_location"],
        15,
        8,
      ],
      "circle-color": [
        "case",
        ["==", ["get", "type"], "project"],
        "#3b82f6",
        ["==", ["get", "type"], "worker"],
        ["case", ["==", ["get", "status"], "compliant"], "#10b981", "#ef4444"],
        ["==", ["get", "type"], "asset"],
        "#f59e0b",
        ["==", ["get", "type"], "work_location"],
        "#8b5cf6",
        "#6b7280",
      ],
      "circle-stroke-width": [
        "case",
        ["boolean", ["feature-state", "selected"], false],
        3,
        ["boolean", ["feature-state", "highlight"], false],
        2,
        1,
      ],
      "circle-stroke-color": [
        "case",
        ["boolean", ["feature-state", "selected"], false],
        "#ffffff",
        ["boolean", ["feature-state", "highlight"], false],
        "#ffffff",
        "#ffffff",
      ],
      "circle-opacity": [
        "case",
        ["boolean", ["feature-state", "highlight"], false],
        0.8,
        0.7,
      ],
    },
  });

  if (!map.value._interactions.get("mapData-click")) {
    map.value.addInteraction("mapData-click", {
      type: "click",
      target: { layerId: "mapData" },
      handler: ({ feature, lngLat }) => {
        if (selectedLocation.value) {
          map.value?.setFeatureState(selectedLocation.value, {
            selected: false,
          });
        }

        if (!feature) {
          selectedLocation.value = undefined;
          return;
        }

        map.value?.setFeatureState(feature, { selected: true });
        selectedLocation.value = feature;
      },
    });

    map.value.addInteraction("mapData-mouseenter", {
      type: "mouseenter",
      target: { layerId: "mapData" },
      handler: ({ feature }) => {
        map.value!.setFeatureState(feature!, { highlight: true });
        map.value!.getCanvas().style.cursor = "pointer";
      },
    });
    map.value.addInteraction("mapData-mouseleave", {
      type: "mouseleave",
      target: { layerId: "mapData" },
      handler: ({ feature }) => {
        map.value!.setFeatureState(feature!, { highlight: false });
        map.value!.getCanvas().style.cursor = "";
        return false;
      },
    });
  }
}

async function loadMapDataSource() {
  if (!map.value) {
    return;
  }
  if (!map.value.getSource("mapData")) {
    map.value.addSource("mapData", {
      type: "geojson",
      data: mapData.value ?? {
        type: "FeatureCollection",
        features: [],
      },
      promoteId: "id",
    });
  } else if (mapData.value) {
    map.value.getSource<GeoJSONSource>("mapData")?.setData(mapData.value);
  }
}

function loadTerrain() {
  if (!map.value) {
    return;
  }
  // Add terrain source, with slight exaggeration
  if (!map.value.getSource("mapbox-dem")) {
    map.value.addSource("mapbox-dem", {
      type: "raster-dem",
      url: "mapbox://mapbox.mapbox-terrain-dem-v1",
      tileSize: 512,
      maxzoom: 10,
    });
  }
  map.value.setTerrain({ source: "mapbox-dem", exaggeration: 1.5 });
}

function focusFeatureCard() {
  if (!selectedLocation.value) {
    return;
  }
  const id = selectedLocation.value.id;
  document.querySelector(`[data-feature-id="${id}"]`)?.scrollIntoView({
    behavior: "smooth",
    block: "center",
  });
}

function updatePopup() {
  if (popup.value) {
    popup.value.remove();
  }
  if (
    !selectedLocation.value ||
    !popupElement.value ||
    !map.value ||
    !mapboxgl
  ) {
    return;
  }

  const feature = selectedLocation.value;
  const coordinates = ((feature.geometry || {}) as any)
    ?.coordinates as LngLatLike;

  popup.value = new mapboxgl.Popup({
    closeOnClick: false,
    closeButton: false,
    className: "",
    offset: 20,
  });

  popup.value.setLngLat(coordinates);
  popup.value.setDOMContent(popupElement.value);
  popup.value.addTo(map.value);

  map.value.flyTo({
    center: coordinates,
    zoom: 15,
    essential: true,
  });
}

function selectFeature(feature?: any) {
  if (selectedLocation.value) {
    map.value?.setFeatureState(selectedLocation.value, { selected: false });
  }

  if (!feature) {
    selectedLocation.value = undefined;
    return;
  }

  selectedLocation.value = {
    ...feature,
    source: "mapData",
    id: feature.id,
  };
  map.value?.setFeatureState(selectedLocation.value!, { selected: true });
}

// Toggle layer visibility
function toggleLayer(layer: string) {
  (layerVisibility.value as any)[layer] = !(layerVisibility.value as any)[
    layer
  ];
}

// Get layer icon
function getLayerIcon(layer: string) {
  const icons: Record<string, string> = {
    projects: "solar:buildings-2-bold-duotone",
    workers: "solar:users-group-rounded-bold-duotone",
    rental_workers: "solar:hard-hat-bold-duotone",
    assets: "solar:box-bold-duotone",
    work_locations: "solar:map-point-bold-duotone",
  };
  return icons[layer] || "solar:question-circle-linear";
}

// Get layer color
function getLayerColor(layer: string) {
  const colors: Record<string, string> = {
    projects: "text-blue-500",
    workers: "text-green-500",
    rental_workers: "text-orange-500",
    assets: "text-amber-500",
    work_locations: "text-purple-500",
  };
  return colors[layer as keyof typeof colors] || "text-gray-500";
}

// Get layer count
function getLayerCount(layer: string) {
  if (!mapData.value?.features) return 0;
  return mapData.value.features.filter(
    (feature: any) => feature.properties.type === layer.slice(0, -1) // Remove 's' from layer name
  ).length;
}
</script>

<template>
  <div
    class="map-container flex -mt-6 h-[calc(100dvh_-_56px)] flex-col lg:flex-row"
  >
    <!-- Sidebar -->
    <div
      class="dark:bg-muted-900 w-full shrink-0 bg-muted-50 h-[calc(100dvh_-_56px)] lg:w-96"
    >
      <div class="nui-slimscroll overflow-y-auto p-6 h-[calc(100dvh_-_56px)]!">
        <!-- Layer Controls -->
        <BaseHeading
          size="xs"
          weight="medium"
          class="mb-4 uppercase tracking-wider flex"
        >
          <span class="text-muted-600 dark:text-muted-400 grow mt-6">{{
            t("map.layers.title")
          }}</span>
          <Icon
            v-if="status === 'pending'"
            name="nui-icon:spiner"
            class="size-4 text-muted-400"
          />
        </BaseHeading>

        <!-- Layer Toggle Cards -->
        <div class="flex flex-col gap-2 mb-6">
          <BaseCard
            v-for="(visible, layer) in layerVisibility"
            :key="layer"
            rounded="md"
            variant="none"
            class="p-2 cursor-pointer transition-all duration-200 border"
            :class="[
              visible
                ? {
                    'border-blue-500': layer === 'projects',
                    'border-green-500': layer === 'workers',
                    'border-orange-500': layer === 'rental_workers',
                    'border-amber-500': layer === 'assets',
                    'border-purple-500': layer === 'work_locations',
                  }
                : 'border-muted-200 dark:border-muted-700 hover:border-muted-300 dark:hover:border-muted-600',
            ]"
            @click="toggleLayer(layer)"
          >
            <div class="flex items-center">
              <div class="relative">
                <BaseProgressCircle
                  :max="100"
                  :model-value="visible ? 100 : 0"
                  :size="50"
                  :thickness="1"
                  variant="none"
                  :class="
                    getLayerColor(layer) +
                    ' *:first:text-muted-200 *:dark:first:text-muted-900'
                  "
                />
                <Icon
                  :name="getLayerIcon(layer)"
                  class="absolute start-1/2 top-1/2 size-4 -translate-x-1/2 -translate-y-1/2"
                  :class="getLayerColor(layer)"
                />
              </div>
              <div class="ml-2">
                <BaseParagraph
                  size="xs"
                  weight="medium"
                  class="uppercase text-muted-600 dark:text-muted-400"
                >
                  {{ t(`map.layers.${layer}`) }}
                </BaseParagraph>
                <BaseHeading
                  size="lg"
                  weight="medium"
                  class="text-muted-900 dark:text-white"
                >
                  {{ getLayerCount(layer) }}
                </BaseHeading>
              </div>
              <!-- <div class="ms-auto me-2">
                <TairoCheckAnimated v-if="visible" color="success" size="md" />
              </div> -->
            </div>
          </BaseCard>
        </div>

        <!-- Feature List -->
        <BaseHeading
          size="xs"
          weight="medium"
          class="mb-4 uppercase tracking-wider flex"
        >
          <span class="text-muted-600 dark:text-muted-400 grow">{{
            t("map.items.title")
          }}</span>
          <BaseText size="xs" class="text-muted-400">
            {{
              t("map.items.count", { count: mapData?.features?.length || 0 })
            }}
          </BaseText>
        </BaseHeading>

        <!-- Cards list -->
        <div class="flex flex-col gap-4">
          <template v-if="status === 'pending' && !mapData?.features">
            <BaseCard
              v-for="index in 5"
              :key="index"
              rounded="md"
              class="p-4 md:p-5"
            >
              <div class="flex items-center gap-2">
                <BasePlaceload class="size-8 rounded-full shrink-0" />
                <div class="space-y-1 grow">
                  <BasePlaceload class="w-1/3 h-2 rounded-md" />
                  <BasePlaceload class="w-1/2 h-2 rounded-md" />
                </div>
              </div>
              <div class="space-y-1 mt-3">
                <BasePlaceload class="w-4/6 h-2 rounded-md" />
                <BasePlaceload class="w-5/6 h-2 rounded-md" />
              </div>
            </BaseCard>
          </template>
          <template v-else-if="mapData?.features">
            <BaseCard
              v-for="(feature, key) in mapData!.features"
              :key="key"
              variant="none"
              class="cursor-pointer p-3 border transition-all duration-200"
              rounded="lg"
              :data-feature-id="feature.properties?.id"
              :class="[
                selectedLocation?.id === feature.id
                  ? {
                      'border-blue-500': feature.properties?.type === 'project',
                      'border-green-500': feature.properties?.type === 'worker',
                      'border-amber-500': feature.properties?.type === 'asset',
                      'border-purple-500':
                        feature.properties?.type === 'work_location',
                    }
                  : 'border-muted-200 dark:border-muted-700 hover:border-muted-300 dark:hover:border-muted-600',
              ]"
              tabindex="0"
              role="button"
              @keydown.space.prevent="selectFeature(feature)"
              @click="selectFeature(feature)"
            >
              <!-- Worker card design -->
              <template v-if="feature.properties?.type === 'worker'">
                <div class="flex w-full items-center gap-2">
                  <BaseAvatar
                    size="sm"
                    :src="feature.properties?.avatar"
                    :text="`${feature.properties?.firstName?.[0] || ''}${
                      feature.properties?.lastName?.[0] || ''
                    }`"
                    class="bg-success-100 text-success-400"
                  />
                  <div>
                    <BaseHeading tag="h3" size="sm" weight="medium">
                      {{ feature.properties?.name }}
                    </BaseHeading>
                    <BaseParagraph
                      size="xs"
                      class="text-muted-600 dark:text-muted-400"
                    >
                      {{ t(`map.types.${feature.properties?.type}`) }}
                      <template v-if="feature.properties?.company">
                        • {{ feature.properties.company }}
                      </template>
                    </BaseParagraph>
                  </div>
                  <div class="ms-auto">
                    <div
                      class="w-6 h-6 rounded-full flex items-center justify-center"
                      :class="[
                        feature.properties.status === 'compliant'
                          ? 'bg-green-500'
                          : feature.properties.status === 'non-compliant'
                          ? 'bg-red-500'
                          : feature.properties.status === 'active'
                          ? 'bg-green-500'
                          : 'bg-gray-400',
                      ]"
                    ></div>
                  </div>
                </div>
              </template>

              <!-- Other item types (projects, assets, work_locations) -->
              <template v-else>
                <div class="relative">
                  <div class="relative mb-3 flex items-center gap-2">
                    <div
                      class="flex items-center justify-center w-8 h-8 rounded-lg"
                      :class="[
                        feature.properties?.type === 'project'
                          ? 'bg-blue-100 dark:bg-blue-900/20'
                          : feature.properties?.type === 'asset'
                          ? 'bg-amber-100 dark:bg-amber-900/20'
                          : feature.properties?.type === 'work_location'
                          ? 'bg-purple-100 dark:bg-purple-900/20'
                          : 'bg-gray-100 dark:bg-gray-900/20',
                      ]"
                    >
                      <Icon
                        :name="getLayerIcon(feature.properties?.type)"
                        class="size-4"
                        :class="getLayerColor(feature.properties?.type)"
                      />
                    </div>
                    <div class="font-sans flex-1">
                      <h4
                        class="text-muted-800 dark:text-muted-100 text-sm font-medium leading-none"
                      >
                        {{ feature.properties?.name }}
                      </h4>
                      <p class="text-muted-400 text-xs">
                        {{ t(`map.types.${feature.properties?.type}`) }}
                        <template v-if="feature.properties?.company">
                          • {{ feature.properties.company }}
                        </template>
                      </p>
                    </div>
                    <div
                      class="ms-auto flex items-center gap-1"
                      v-if="feature.properties?.status"
                    >
                      <div
                        class="w-6 h-6 rounded-full flex items-center justify-center"
                        :class="[
                          feature.properties.status === 'ACTIVE'
                            ? 'bg-green-500'
                            : feature.properties.status === 'INACTIVE'
                            ? 'bg-gray-400'
                            : feature.properties.status === 'MAINTENANCE'
                            ? 'bg-yellow-500'
                            : 'bg-gray-400',
                        ]"
                      >
                        <Icon
                          name="lucide:check"
                          class="size-3 text-white"
                          v-if="feature.properties.status === 'ACTIVE'"
                        />
                        <Icon
                          name="lucide:wrench"
                          class="size-3 text-white"
                          v-else-if="
                            feature.properties.status === 'MAINTENANCE'
                          "
                        />
                        <Icon
                          name="lucide:x"
                          class="size-3 text-white"
                          v-else
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    class="text-muted-500 dark:text-muted-400 font-sans text-xs"
                  >
                    <p v-if="feature.properties?.description">
                      {{ feature.properties.description }}
                    </p>
                    <p v-else-if="feature.properties?.project">
                      {{ t("map.info.project") }}:
                      {{ feature.properties.project }}
                    </p>
                    <p v-else-if="feature.properties?.address">
                      {{ feature.properties.address }}
                    </p>
                  </div>

                  <div
                    v-if="selectedLocation?.id === feature.id"
                    class="mt-3 flex items-center gap-2"
                  >
                    <BaseButton size="sm" variant="primary">
                      {{ t("map.actions.view_details") }}
                    </BaseButton>
                    <BaseButton size="sm" variant="muted">
                      {{ t("map.actions.track") }}
                    </BaseButton>
                    <div
                      class="ms-auto text-muted-400 flex items-center gap-1 font-sans text-xs"
                      v-if="feature.properties?.timestamp"
                    >
                      <Icon name="lucide:clock" class="size-3" />
                      <span class="dark-inverted">
                        {{
                          new Date(
                            feature.properties.timestamp
                          ).toLocaleTimeString()
                        }}
                      </span>
                    </div>
                  </div>
                </div>
              </template>
            </BaseCard>
          </template>
        </div>
      </div>
    </div>

    <!-- Map -->
    <div class="relative grow h-[calc(100dvh_-_56px)]">
      <div
        v-if="selectedLocation"
        ref="popupElement"
        class="starting:opacity-0 transition-discrete duration-300 transition-all"
      >
        <MapMarker
          v-bind="selectedLocation.properties"
          @close="selectFeature()"
        />
      </div>
      <div ref="mapElement" class="absolute inset-0 size-full">
        <div
          class="absolute inset-0 bg-muted-100 dark:bg-muted-800 flex items-center justify-center flex-col"
        >
          <template v-if="isLoading">
            <Icon
              name="nui-icon:spiner"
              class="size-5 text-muted-400 dark:text-muted-500"
            />
          </template>
          <template v-else-if="hasError">
            <DevOnly>
              <BaseHeading size="lg">
                {{ t("map.errors.invalid_token") }}
              </BaseHeading>
              <BaseParagraph class="text-muted-500">
                {{ t("map.errors.token_message") }}
              </BaseParagraph>
            </DevOnly>
          </template>
        </div>
      </div>

      <div class="group/modes absolute bottom-8 start-2">
        <div class="flex items-center gap-3">
          <BaseTooltip
            :content="`Mode: ${mode}`"
            :bindings="{ portal: { disabled: true } }"
          >
            <div
              role="button"
              class="group/button flex items-center justify-center size-20 border border-muted-300 dark:border-muted-800 rounded-xl bg-white dark:bg-muted-950 shadow-lg"
            >
              <img
                :src="`/img/illustrations/maps/${mode}.svg`"
                alt="Map mode"
                class="size-[4.35rem] rounded-lg group-hover/button:scale-95 transition-all duration-200"
              />
            </div>
          </BaseTooltip>
          <div
            class="flex items-center gap-3 h-20 border border-muted-300 dark:border-muted-800 rounded-lg bg-white dark:bg-muted-950 shadow-lg px-3 pointer-events-none group-hover/modes:pointer-events-auto opacity-0 group-hover/modes:opacity-100 transition-opacity duration-200"
          >
            <div
              v-for="modeId in (['base', 'navigation', 'satellite'] as const)"
              :key="modeId"
              class="group/mode"
            >
              <button
                type="button"
                class="cursor-pointer text-center flex flex-col items-center gap-0.5"
                @click="
                  () => {
                    mode = modeId;
                    updateMapStyle();
                  }
                "
              >
                <img
                  :src="`/img/illustrations/maps/${modeId}.svg`"
                  alt="Map mode"
                  class="size-10 rounded-md group-hover/mode:grayscale-0 transition-all duration-200"
                  :class="
                    mode === modeId
                      ? 'ring-2 ring-primary-500 dark:ring-primary-400'
                      : 'grayscale opacity-60'
                  "
                />
                <BaseText
                  size="xs"
                  weight="medium"
                  class="capitalize text-muted-400 group-hover/mode:text-muted-900 dark:group-hover/mode:text-muted-100 transition-colors duration-200"
                  :class="
                    mode === modeId
                      ? 'text-muted-900 dark:text-muted-100'
                      : 'text-muted-400'
                  "
                >
                  {{ t(`map.modes.${modeId}`) }}
                </BaseText>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
@reference '~/assets/main.css';

.map-container {
  .mapboxgl-popup-content {
    @apply p-4 rounded-lg shadow-xl shadow-muted-300/30 dark:shadow-muted-900/40 border border-muted-200 dark:border-muted-700;
    background-color: var(--color-card-default-bg, white);
    min-width: 280px;
    max-width: 400px;
    width: max-content;
  }

  .dark .mapboxgl-popup-content {
    background-color: var(--color-card-default-bg, var(--color-muted-900));
  }
}
</style>
