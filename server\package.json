{"name": "comanager-server", "version": "1.0.0", "private": true, "type": "commonjs", "scripts": {"build:schema": "node prisma/build-schema.mjs", "prisma:generate": "npm run build:schema && prisma generate", "prisma:migrate": "npm run build:schema && prisma migrate dev", "seed:subscription-plans": "tsx prisma/seed/subscription-plans.ts", "seed:users": "cross-env dotenv -e .env.dev -- tsx prisma/seed/user-data.ts", "seed:chat": "cross-env dotenv -e .env.dev -- tsx prisma/seed/chat-data.ts", "seed:calendar": "cross-env dotenv -e .env.dev -- tsx prisma/seed/calendar-seed.ts", "seed:map": "cross-env dotenv -e .env.dev -- tsx prisma/seed/map-data.ts", "update:prisma-imports": "node scripts/update-prisma-imports.js", "dev": "cross-env dotenv -e .env.dev -- nodemon --ext ts --exec tsx index.ts", "check": "tsc --noEmit", "build": "rimraf dist && cross-env dotenv -e .env.production -- tsc", "start": "cross-env dotenv -e .env.production -- node dist/src/index.js"}, "engines": {"node": ">=22.0.0"}, "dependencies": {"@prisma/client": "^6.8.2", "@prisma/extension-optimize": "^1.1.8", "@prisma/instrumentation": "^6.8.2", "bcrypt": "^6.0.0", "connect-redis": "^7.1.1", "cors": "^2.8.5", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "express": "^5.1.0", "express-session": "^1.18.1", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.2", "multer": "1.4.5-lts.2", "nanoid": "^5.1.5", "node-cron": "^4.0.5", "nodemailer": "^7.0.3", "openai": "^4.100.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "qrcode": "^1.5.4", "redis": "^4.6.13", "rimraf": "^6.0.1", "slugify": "^1.6.6", "stripe": "^18.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/express-serve-static-core": "^5.0.6", "@types/express-session": "^1.18.1", "@types/http-errors": "^2.0.4", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.19", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/qrcode": "^1.5.5", "nodemon": "^3.1.10", "prisma": "^6.8.2", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}