// server\prisma\schema\15-map-tracking.prisma

// GPS Device for asset tracking
model GpsDevice {
  id          Int      @id @default(autoincrement())
  deviceId    String   @unique // Unique identifier from GPS device
  name        String   // Human readable name
  description String?
  isActive    Boolean  @default(true)
  batteryLevel Float?  // Battery percentage (0-100)
  lastSeen    DateTime?
  
  // Device configuration
  reportingInterval Int @default(300) // Seconds between reports
  geofenceRadius   Float? // Meters for geofence alerts
  
  // Relations
  assetId     Int?
  asset       TrackableAsset?   @relation(fields: [assetId], references: [id])
  
  // Location history
  locationHistory GpsLocation[]
  
  // Company relation
  companyId   Int
  company     Company  @relation(fields: [companyId], references: [id])
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("gps_devices")
}

// GPS Location data points
model GpsLocation {
  id          Int      @id @default(autoincrement())
  latitude    Float
  longitude   Float
  altitude    Float?
  accuracy    Float?   // GPS accuracy in meters
  speed       Float?   // Speed in km/h
  heading     Float?   // Direction in degrees (0-360)
  timestamp   DateTime
  
  // Device relation
  deviceId    Int
  device      GpsDevice @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  
  // Additional metadata
  batteryLevel Float?
  signalStrength Float?
  
  createdAt   DateTime @default(now())
  
  @@map("gps_locations")
  @@index([deviceId, timestamp])
  @@index([latitude, longitude])
}

// Trackable Asset (equipment, vehicles, etc. with GPS tracking)
model TrackableAsset {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  type        TrackableAssetType
  status      TrackableAssetStatus @default(ACTIVE)

  // Asset details
  serialNumber String?
  model       String?
  manufacturer String?
  purchaseDate DateTime?
  purchasePrice Float?
  currentValue Float?

  // Location tracking
  gpsDevices  GpsDevice[]
  currentLocation TrackableAssetLocation?

  // Company relation
  companyId   Int
  company     Company  @relation(fields: [companyId], references: [id])

  // Project assignments
  projectAssignments TrackableAssetAssignment[]

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("trackable_assets")
}

// Current location of trackable assets (cached for performance)
model TrackableAssetLocation {
  id          Int      @id @default(autoincrement())
  assetId     Int      @unique
  asset       TrackableAsset    @relation(fields: [assetId], references: [id], onDelete: Cascade)

  latitude    Float
  longitude   Float
  address     String?
  lastUpdate  DateTime

  // Geofence status
  isInGeofence Boolean @default(true)
  geofenceName String?

  @@map("trackable_asset_locations")
}

// Trackable asset assignments to projects
model TrackableAssetAssignment {
  id          Int      @id @default(autoincrement())
  assetId     Int
  asset       TrackableAsset    @relation(fields: [assetId], references: [id])
  projectId   Int
  project     Project  @relation(fields: [projectId], references: [id])

  assignedAt  DateTime @default(now())
  assignedBy  Int
  assignedByUser User  @relation("AssetAssignments", fields: [assignedBy], references: [id])

  unassignedAt DateTime?
  unassignedBy Int?
  unassignedByUser User? @relation("AssetUnassignments", fields: [unassignedBy], references: [id])

  notes       String?

  @@map("trackable_asset_assignments")
  @@unique([assetId, projectId, assignedAt])
}

// Worker location tracking (for time management)
model WorkerLocation {
  id          Int      @id @default(autoincrement())
  workerId    Int
  worker      User     @relation("WorkerLocations", fields: [workerId], references: [id])
  
  latitude    Float
  longitude   Float
  accuracy    Float?
  timestamp   DateTime
  
  // Work context
  projectId   Int?
  project     Project? @relation("WorkerProjectLocations", fields: [projectId], references: [id])
  taskId      Int?
  task        Task?    @relation("WorkerTaskLocations", fields: [taskId], references: [id])
  
  // Time entry relation
  timeEntryId Int?
  timeEntry   TimeEntry? @relation(fields: [timeEntryId], references: [id])
  
  // Geofence compliance
  isCompliant Boolean @default(true)
  workLocationId Int?
  workLocation WorkLocation? @relation(fields: [workLocationId], references: [id])
  
  createdAt   DateTime @default(now())
  
  @@map("worker_locations")
  @@index([workerId, timestamp])
  @@index([projectId, timestamp])
}

// Map layer configurations for different user roles
model MapLayer {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  type        MapLayerType
  isDefault   Boolean  @default(false)
  isActive    Boolean  @default(true)
  
  // Role-based access
  allowedRoles UserRole[]
  
  // Layer configuration
  config      Json?    // Layer-specific configuration
  
  // Company relation
  companyId   Int
  company     Company  @relation(fields: [companyId], references: [id])
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("map_layers")
}

// Enums
enum TrackableAssetType {
  VEHICLE
  EQUIPMENT
  TOOL
  MACHINERY
  DEVICE
  OTHER
}

enum TrackableAssetStatus {
  ACTIVE
  INACTIVE
  MAINTENANCE
  REPAIR
  LOST
  STOLEN
  DISPOSED
}

enum MapLayerType {
  PROJECTS
  WORKERS
  ASSETS
  WORK_LOCATIONS
  GEOFENCES
  CUSTOM
}
