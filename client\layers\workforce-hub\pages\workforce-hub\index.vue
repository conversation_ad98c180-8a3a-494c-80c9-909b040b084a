<script setup lang="ts">
const { t } = useI18n();

definePageMeta({
  title: "Workforce Hub",
});

// Mock data for demonstration
const workerStats = ref({
  total: 48,
  verified: 32,
  available: 18,
  assigned: 30,
});

const jobStats = ref({
  total: 25,
  open: 8,
  inProgress: 12,
  completed: 5,
});

const matchStats = ref({
  total: 120,
  pending: 35,
  accepted: 42,
  rejected: 43,
});

const assignmentStats = ref({
  total: 67,
  active: 23,
  completed: 38,
  trial: 6,
});

const recentMatches = ref([
  {
    id: "1",
    worker: {
      firstName: "Jaan",
      lastName: "Tamm",
      avatar: "",
      specialization: "Welder",
    },
    jobRequest: {
      title: "Senior Welder for Construction Project",
      company: { name: "BuildTech Construction" },
    },
    matchScore: 92,
    status: "pending",
  },
  {
    id: "2", 
    worker: {
      firstName: "Mart",
      lastName: "Kask",
      avatar: "",
      specialization: "Plumber",
    },
    jobRequest: {
      title: "Experienced Plumber Team",
      company: { name: "Hydro Solutions" },
    },
    matchScore: 87,
    status: "accepted",
  },
  {
    id: "3",
    worker: {
      firstName: "Tiina",
      lastName: "Lepp", 
      avatar: "",
      specialization: "Electrician",
    },
    jobRequest: {
      title: "Electrician for Office Building",
      company: { name: "PowerGrid Systems" },
    },
    matchScore: 78,
    status: "rejected",
  },
]);

const recentAssignments = ref([
  {
    id: "1",
    worker: {
      firstName: "Peeter",
      lastName: "Saar",
      avatar: "",
    },
    jobRequest: {
      title: "Construction Site Supervisor",
      company: { name: "Nordic Build" },
    },
    startDate: "2024-01-15",
    status: "active",
    rate: 25.50,
  },
  {
    id: "2",
    worker: {
      firstName: "Liisa",
      lastName: "Mets",
      avatar: "",
    },
    jobRequest: {
      title: "Safety Inspector",
      company: { name: "SafeWork Solutions" },
    },
    startDate: "2024-01-10",
    status: "trial",
    rate: 22.00,
  },
]);

function getStatusColor(status: string) {
  const colors: Record<string, string> = {
    pending: "warning",
    accepted: "success", 
    rejected: "danger",
    active: "success",
    trial: "info",
    completed: "muted",
  };
  return colors[status] || "muted";
}

function formatCurrency(amount: number) {
  return new Intl.NumberFormat("et-EE", {
    style: "currency",
    currency: "EUR",
  }).format(amount);
}
</script>

<template>
  <div class="p-6 dark:bg-black bg-muted-10">
    <!-- Header -->
    <BaseCard class="mb-6 p-6 dark:bg-black bg-white">
      <div class="flex items-center justify-between mb-4">
        <div>
          <BaseHeading
            as="h2"
            size="xl"
            weight="medium"
            class="mb-1 text-muted-900 dark:text-white"
          >
            {{ t("workforce_hub.dashboard.title") }}
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-400">
            {{ t("workforce_hub.dashboard.subtitle") }}
          </BaseParagraph>
        </div>
        <div class="flex gap-2">
          <BaseButton variant="primary" to="/workforce-hub/job-requests/create">
            <Icon name="solar:case-plus-linear" class="h-5 w-5 mr-2" />
            {{ t("workforce_hub.job_requests.create_new") }}
          </BaseButton>
        </div>
      </div>
    </BaseCard>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Worker Stats -->
      <BaseCard class="p-6 dark:bg-black bg-white">
        <div class="flex items-center gap-4">
          <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-blue-100 dark:bg-blue-900/20">
            <Icon name="solar:users-group-rounded-bold-duotone" class="h-6 w-6 text-blue-500" />
          </div>
          <div>
            <BaseText size="xs" class="text-muted-400 uppercase tracking-wider">
              {{ t("workforce_hub.dashboard.stats.total_workers") }}
            </BaseText>
            <BaseHeading size="lg" weight="semibold" class="text-muted-900 dark:text-white">
              {{ workerStats.total }}
            </BaseHeading>
          </div>
        </div>
        <div class="mt-4 grid grid-cols-3 gap-2 text-xs">
          <div class="text-center">
            <div class="font-medium text-green-600">{{ workerStats.verified }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.dashboard.stats.verified_workers") }}</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-blue-600">{{ workerStats.available }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.dashboard.stats.available_workers") }}</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-amber-600">{{ workerStats.assigned }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.dashboard.stats.assigned_workers") }}</div>
          </div>
        </div>
      </BaseCard>

      <!-- Job Stats -->
      <BaseCard class="p-6 dark:bg-black bg-white">
        <div class="flex items-center gap-4">
          <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-green-100 dark:bg-green-900/20">
            <Icon name="solar:case-bold-duotone" class="h-6 w-6 text-green-500" />
          </div>
          <div>
            <BaseText size="xs" class="text-muted-400 uppercase tracking-wider">
              {{ t("workforce_hub.dashboard.stats.total_jobs") }}
            </BaseText>
            <BaseHeading size="lg" weight="semibold" class="text-muted-900 dark:text-white">
              {{ jobStats.total }}
            </BaseHeading>
          </div>
        </div>
        <div class="mt-4 grid grid-cols-3 gap-2 text-xs">
          <div class="text-center">
            <div class="font-medium text-green-600">{{ jobStats.open }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.dashboard.stats.open_jobs") }}</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-blue-600">{{ jobStats.inProgress }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.dashboard.stats.in_progress_jobs") }}</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-amber-600">{{ jobStats.completed }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.dashboard.stats.completed_jobs") }}</div>
          </div>
        </div>
      </BaseCard>

      <!-- Match Stats -->
      <BaseCard class="p-6 dark:bg-black bg-white">
        <div class="flex items-center gap-4">
          <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-purple-100 dark:bg-purple-900/20">
            <Icon name="solar:hand-shake-bold-duotone" class="h-6 w-6 text-purple-500" />
          </div>
          <div>
            <BaseText size="xs" class="text-muted-400 uppercase tracking-wider">
              {{ t("workforce_hub.dashboard.stats.total_matches") }}
            </BaseText>
            <BaseHeading size="lg" weight="semibold" class="text-muted-900 dark:text-white">
              {{ matchStats.total }}
            </BaseHeading>
          </div>
        </div>
        <div class="mt-4 grid grid-cols-3 gap-2 text-xs">
          <div class="text-center">
            <div class="font-medium text-amber-600">{{ matchStats.pending }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.dashboard.stats.pending_matches") }}</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-green-600">{{ matchStats.accepted }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.dashboard.stats.accepted_matches") }}</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-red-600">{{ matchStats.rejected }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.dashboard.stats.rejected_matches") }}</div>
          </div>
        </div>
      </BaseCard>

      <!-- Assignment Stats -->
      <BaseCard class="p-6 dark:bg-black bg-white">
        <div class="flex items-center gap-4">
          <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-amber-100 dark:bg-amber-900/20">
            <Icon name="solar:clipboard-text-bold-duotone" class="h-6 w-6 text-amber-500" />
          </div>
          <div>
            <BaseText size="xs" class="text-muted-400 uppercase tracking-wider">
              {{ t("workforce_hub.assignments.title") }}
            </BaseText>
            <BaseHeading size="lg" weight="semibold" class="text-muted-900 dark:text-white">
              {{ assignmentStats.total }}
            </BaseHeading>
          </div>
        </div>
        <div class="mt-4 grid grid-cols-3 gap-2 text-xs">
          <div class="text-center">
            <div class="font-medium text-green-600">{{ assignmentStats.active }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.assignments.filters.active") }}</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-blue-600">{{ assignmentStats.trial }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.assignments.filters.trial") }}</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-muted-600">{{ assignmentStats.completed }}</div>
            <div class="text-muted-400">{{ t("workforce_hub.assignments.filters.completed") }}</div>
          </div>
        </div>
      </BaseCard>
    </div>
  </div>
</template>
