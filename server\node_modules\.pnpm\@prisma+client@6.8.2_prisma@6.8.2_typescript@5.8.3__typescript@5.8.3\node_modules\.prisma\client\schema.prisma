// This file is auto-generated. Do not edit directly.
// Learn more: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Content from 00-base.prisma
enum PaymentMethod {
  BANK
  CASH
  CRYPTO
  CARD
  BANK_TRANSFER
  CREDIT_CARD
  CHECK
  OTHER
}

enum DocumentType {
  ID_CARD
  PASSPORT
  WORK_PERMIT
  CERTIFICATION
  CONTRACT
  HEALTH_CHECK
  SPECIFICATION
  DRAWING
  INSTRUCTION
  REPORT
  OTHER
}

enum IntervalType {
  ONCE
  MULTIPLE
  RECURRING
}

enum CompanyType {
  CLIENT
  SUPPLIER
  PARTNER_AGENCY
  INDIVIDUAL_CLIENT
  COMPANY_CLIENT
}

enum CompanyStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum RequestStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

enum Duration {
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
  YEARLY
}

enum ContractStatus {
  DRAFT
  ACTIVE
  SUSPENDED
  TERMINATED
}

enum IncomeSource {
  SALARY
  DIVIDENDS
  SOCIAL_BENEFITS
  LOAN
  GIFT
  FREELANCE
  BUSINESS
  OTHER
}

enum ExpenseSource {
  RENT
  GROCERY
  UTILITIES
  TRANSPORT
  SUBSCRIPTION
  LOAN_PAYMENT
  ENTERTAINMENT
  MEDICAL
  EDUCATION
  TRAINING
  ALIMONY
  OTHER
}

enum AssetMethod {
  BANK
  CRYPTO
  CASH
  DEBTORS
  SAVINGS
  STOCKS
  BONDS
  OTHER
}

enum Role {
  SUPERADMIN
  ADMIN
  PROJECTLEADER
  SALESMAN
  WORKER
  CLIENT
  SUPPORTER
}

enum WorkerStatus {
  AVAILABLE
  EMPLOYED
  ON_RENTAL
  INACTIVE
  BLACKLISTED
}

enum RentalStatus {
  PENDING
  ACTIVE
  COMPLETED
  CANCELLED
}

enum RuleType {
  BUDGET_CONSTRAINT
  SKILL_REQUIREMENT
  CERTIFICATION_REQUIREMENT
  WORKER_MATCHING
  RISK_ASSESSMENT
  COMPLIANCE
}

enum RuleSeverity {
  INFO
  WARNING
  ERROR
  CRITICAL
}

enum AccountType {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

enum EntryStatus {
  DRAFT
  POSTED
  VOIDED
}

enum ProjectStatus {
  PLANNING
  IN_PROGRESS
  ON_HOLD
  COMPLETED
  CANCELLED
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  REVIEW
  COMPLETED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

// Employee-related models (from 10-hr.prisma)

// Enums for Employee models
enum EmployeeStatus {
  ACTIVE
  INACTIVE
  ON_LEAVE
  TERMINATED
  SUSPENDED
}

enum EmploymentType {
  FULL_TIME
  PART_TIME
  CONTRACT
  TEMPORARY
  INTERN
}

enum LeaveType {
  VACATION
  SICK
  PERSONAL
  MATERNITY
  PATERNITY
  BEREAVEMENT
  UNPAID
  OTHER
}

enum LeaveStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

enum PayrollStatus {
  DRAFT
  PROCESSING
  COMPLETED
  CANCELLED
}

enum PerformanceRating {
  EXCELLENT
  GOOD
  SATISFACTORY
  NEEDS_IMPROVEMENT
  POOR
}

enum BonusType {
  PERFORMANCE
  ANNUAL
  REFERRAL
  SIGNING
  RETENTION
  PROJECT
  HOLIDAY
  SPOT
  OTHER
}

// New enums for HR module
enum BenefitType {
  HEALTH_INSURANCE
  DENTAL_INSURANCE
  VISION_INSURANCE
  LIFE_INSURANCE
  DISABILITY
  RETIREMENT
  STOCK_OPTIONS
  WELLNESS
  EDUCATION
  CHILDCARE
  TRANSPORTATION
  MEAL
  OTHER
}

enum BenefitStatus {
  ACTIVE
  INACTIVE
  PENDING
  EXPIRED
}

enum TrainingType {
  ONBOARDING
  TECHNICAL
  SOFT_SKILLS
  COMPLIANCE
  LEADERSHIP
  SAFETY
  PRODUCT
  CERTIFICATION
  OTHER
}

enum TrainingStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum DocumentCategory {
  PERSONAL
  EMPLOYMENT
  PAYROLL
  BENEFITS
  PERFORMANCE
  TRAINING
  LEGAL
  OTHER
}

enum TransactionType {
  INCOME
  EXPENSE
}

enum TransactionStatus {
  COMPLETED
  PENDING
  FAILED
}

enum BudgetType {
  PERSONAL
  COMPANY
}

enum GoalType {
  SAVING
  SPENDING
}

enum GoalStatus {
  IN_PROGRESS
  COMPLETED
  FAILED
}

// Email types
enum EmailStatus {
  READ
  UNREAD
  FLAGGED
  DELETED
}

enum EmailPriority {
  HIGH
  NORMAL
  LOW
}

enum EmailProvider {
  GMAIL
  OUTLOOK
  ZOHO
  CUSTOM
}

// Endpoint types
enum EndpointMethod {
  GET
  POST
  PUT
  DELETE
}

enum EndpointStatus {
  ACTIVE
  INACTIVE
  DEPRECATED
}

// Chat types
enum ChatMessageStatus {
  SENT
  DELIVERED
  READ
  FAILED
}

enum ChatConversationType {
  DIRECT
  GROUP
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  TRIAL
  EXPIRED
  CANCELLED
  PENDING_PAYMENT
}

enum SubscriptionPlanType {
  FREE_TRIAL
  STARTER
  PREMIUM
}

enum BillingCycle {
  MONTHLY
  YEARLY
}

// Sales Module Schema

// Enums
enum LeadStatus {
  NEW
  CONTACTED
  QUALIFIED
  UNQUALIFIED
  CONVERTED
  LOST
}

enum LeadSource {
  WEBSITE
  REFERRAL
  SOCIAL_MEDIA
  EMAIL_CAMPAIGN
  COLD_CALL
  EVENT
  PARTNER
  ADVERTISEMENT
  OTHER
}

enum DealStage {
  PROSPECTING
  QUALIFICATION
  NEEDS_ANALYSIS
  VALUE_PROPOSITION
  PROPOSAL
  NEGOTIATION
  CLOSED_WON
  CLOSED_LOST
}

enum DealType {
  NEW_BUSINESS
  EXISTING_BUSINESS
  RENEWAL
  UPSELL
  CROSS_SELL
}

enum ActivityType {
  CALL
  EMAIL
  MEETING
  TASK
  NOTE
  FOLLOW_UP
  DEMO
  PRESENTATION
}

enum ActivityStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum CampaignType {
  EMAIL
  SOCIAL_MEDIA
  EVENT
  WEBINAR
  DIRECT_MAIL
  TELEMARKETING
  CONTENT
  REFERRAL_PROGRAM
  PARTNER
}

enum CampaignStatus {
  DRAFT
  SCHEDULED
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

enum ProductCategory {
  PHYSICAL
  DIGITAL
  SERVICE
  SUBSCRIPTION
}

enum QuotationStatus {
  DRAFT
  SENT
  VIEWED
  ACCEPTED
  REJECTED
  EXPIRED
}

// Enums for Time Management module
enum TimeEntryStatus {
  ACTIVE
  COMPLETED
  PAUSED
  REJECTED
}

enum TimeEntryApprovalStatus {
  PENDING
  APPROVED
  REJECTED
  MODIFIED
}

enum TimesheetStatus {
  DRAFT
  SUBMITTED
  APPROVED
  REJECTED
}

enum AssignmentStatus {
  PENDING
  ACCEPTED
  REJECTED
  COMPLETED
  CANCELLED
}

enum SuggestionStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ClientAccessLevel {
  VIEW_ONLY
  DETAILED
  FULL_ACCESS
}

// Enums
enum FormStatus {
  draft
  published
  archived
  expired
}

enum FieldType {
  text
  textarea
  email
  phone
  number
  date
  time
  datetime
  select
  multiselect
  radio
  checkbox
  file
  image
  address
  name
  url
  password
  hidden
  heading
  paragraph
  divider
  signature
  rating
  slider
}

enum SubmissionStatus {
  new
  reviewed
  shortlisted
  contacted
  interviewed
  hired
  rejected
  archived
}

// Enums
enum VerificationStatus {
  pending
  inProgress
  verified
  rejected
}

enum HealthStatus {
  passed
  conditionalPass
  failed
  expired
}

enum ExperienceLevel {
  entry
  intermediate
  expert
  master
}

enum WorkingHoursType {
  fullTime
  partTime
  flexible
  shifts
}

enum RateType {
  hourly
  daily
  weekly
  monthly
  fixed
}

enum JobRequestStatus {
  draft
  open
  inProgress
  filled
  completed
  cancelled
}

enum UrgencyLevel {
  low
  normal
  high
  critical
}

enum MatchStatus {
  pending
  accepted
  rejected
  expired
}

enum WorkerAssignmentStatus {
  PENDING
  ACTIVE
  COMPLETED
  TERMINATED
  EXTENDED
}

enum PartnershipLevel {
  standard
  premium
  exclusive
}

enum WorkforceApplicationStatus {
  new
  contacted
  reviewed
  hired
  rejected
}

// Recruitment enums
enum JobStatus {
  DRAFT
  ACTIVE
  PAUSED
  CLOSED
  EXPIRED
}

enum ApplicationStatus {
  PENDING
  REVIEWED
  SHORTLISTED
  REJECTED
  HIRED
}

enum ApplicationStage {
  APPLIED
  SCREENING
  INTERVIEW
  ASSESSMENT
  REFERENCE_CHECK
  OFFER
  HIRED
  REJECTED
}

enum InterviewType {
  PHONE
  VIDEO
  IN_PERSON
  TECHNICAL
  PANEL
}

enum InterviewStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  RESCHEDULED
}

enum AssessmentType {
  TECHNICAL
  BEHAVIORAL
  COGNITIVE
  PERSONALITY
  SKILLS
}

enum EmailTemplateType {
  APPLICATION_RECEIVED
  APPLICATION_REJECTED
  INTERVIEW_INVITATION
  INTERVIEW_REMINDER
  OFFER_LETTER
  WELCOME_MESSAGE
}

// Content from 01-user.prisma
model User {
  id                     Int                  @id @default(autoincrement())
  email                  String               @unique
  password               String?
  firstName              String
  lastName               String
  birthdate              DateTime?
  phone                  String?
  userRoles              UserRole[]
  address                String?
  address2               String?
  street                 String?
  city                   String?
  postalCode             String?
  state                  String?
  country                String?
  notes                  String?
  avatar                 String? // Profile picture URL
  coverImage             String? // Cover photo URL
  createdAt              DateTime             @default(now())
  updatedAt              DateTime             @updatedAt
  emailConfirmationToken String?
  emailConfirmed         Boolean              @default(false)
  phoneConfirmed         Boolean              @default(false)
  sessionLogs            SessionLog[]
  referredByUserId       Int?
  referredBy             User?                @relation("UserReferrals", fields: [referredByUserId], references: [id])
  referrals              User[]               @relation("UserReferrals")
  googleId               String?              @unique
  barcode                String               @unique
  qrCode                 String               @unique
  stripeCustomerId       String?
  companies              UserCompany[]
  // New subscription relations
  legacySubscriptions    LegacySubscription[] @relation("LegacySubscriptions")
  subscriptions          Subscription[]       @relation("Subscriptions")

  // Company join requests
  companyJoinRequests CompanyJoinRequest[]

  // Added opposite relation fields
  incomes  Income[]  @relation("UserIncomes") // For Income.user
  expenses Expense[] @relation("UserExpenses") // For Expense.user
  assets   Asset[]   @relation("UserAssets") // For Asset.user

  CommunicationEndpoint CommunicationEndpoint[]

  ChatMessage ChatMessage[]

  ChatParticipant ChatParticipant[]

  // Production module relations
  taskAssignments       TaskAssignment[]
  createdChecklists     QualityChecklist[]     @relation("ChecklistCreator")
  reviewedChecklists    QualityChecklist[]     @relation("ChecklistReviewer")
  photoEvidences        PhotoEvidence[]
  projectDocuments      ProjectDocument[]
  equipmentMaintenances EquipmentMaintenance[]
  materialTransactions  MaterialTransaction[]
  resourceAllocations   ResourceAllocation[]
  productionReports     ProductionReport[]

  // Budget module relations
  budgets      Budget[]      @relation("UserBudgets")
  transactions Transaction[] @relation("UserTransactions")
  budgetGoals  BudgetGoal[]  @relation("UserBudgetGoals")

  // Sales module relations
  assignedLeads      Lead[]
  assignedDeals      Deal[]
  assignedActivities Activity[]
  createdCampaigns   Campaign[]
  createdDealNotes   DealNote[]
  createdQuotations  Quotation[]
  salesGoals         SalesGoal[]

  // Time management module relations
  timeEntries     TimeEntry[]
  timesheets      Timesheet[]
  workAssignments WorkAssignment[]
  taskSuggestions TaskSuggestion[]
  payrollEntries  PayrollEntry[]
  clientAccesses  ClientAccess[]   @relation("ClientAccess")

  EmailAccount EmailAccount[]

  // HR relations
  managedEmployees     Employee[]          @relation("EmployeeManager")
  createdEmployees     Employee[]          @relation("EmployeeCreator")
  approvedLeaves       LeaveRequest[]
  processedPayrolls    Payroll[]
  approvedBonuses      Bonus[]
  performanceReviews   PerformanceReview[]
  mentorCareerPaths    CareerPath[]
  approvedBonusesBasic BonusBasic[]
  mentorshipsBasic     CareerPathBasic[]
  uploadedHrDocuments  HrDocument[]

  Employee Employee[]

  // Company transition relation
  companyTransitions CompanyTransition[]

  // Supporter payments relation
  supporterPayments Payment[] @relation("SupporterPayments")

  // Recruitment relations
  createdJobs          JobPosting[]
  jobApplications      JobApplication[]
  reviewedApplications JobApplication[]   @relation("ApplicationReviewer")
  interviews           Interview[]
  createdAssessments   Assessment[]
  assessmentResults    AssessmentResult[]

  // Calendar relations
  createdEvents       CalendarEvent[]
  eventParticipations EventParticipant[]
  calendarSettings    CalendarSettings?
  eventReminders      EventReminder[]

  // Map and location tracking relations
  workerLocations    WorkerLocation[]           @relation("WorkerLocations")
  assetAssignments   TrackableAssetAssignment[] @relation("AssetAssignments")
  assetUnassignments TrackableAssetAssignment[] @relation("AssetUnassignments")
}

model SessionLog {
  id       Int       @id @default(autoincrement())
  userId   Int
  user     User      @relation(fields: [userId], references: [id])
  loginAt  DateTime  @default(now())
  logoutAt DateTime?
}

model UserRole {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id])
  role      Role
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Map layer access
  mapLayers MapLayer[] @relation("MapLayerRoles")

  @@unique([userId, role])
}

model UserCompany {
  id        Int      @id @default(autoincrement())
  user      User     @relation(fields: [userId], references: [id])
  userId    Int
  company   Company  @relation(fields: [companyId], references: [id])
  companyId Int
  role      String
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, companyId])
}

// Content from 02-company.prisma
model Company {
  id            Int           @id @default(autoincrement())
  name          String
  type          CompanyType
  status        CompanyStatus
  logo          String? // Company logo URL
  coverImage    String? // Company cover image URL
  creditRating  Float?
  contractTerms Json?

  // Basic Information
  website            String?
  email              String?
  phone              String?
  registrationNumber String?
  vatNumber          String?
  industry           String?
  description        String?
  foundedYear        Int?
  employeeCount      Int?
  annualRevenue      Float?
  isIndividual       Boolean @default(false) // Whether this is an individual client rather than a company

  // Address Information
  address         String?
  address2        String?
  city            String?
  postalCode      String?
  state           String?
  country         String?
  notes           String?
  workforceNeeds  WorkforceNeed[]
  financialHealth FinancialHealth?
  riskAssessment  RiskAssessment?
  contacts        Contact[]
  agreements      Agreement[]
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  barcode         String?          @unique
  qrCode          String?          @unique

  // Added opposite relation fields
  workers           Worker[]           @relation("CurrentEmployer") // For Worker.currentCompany
  laborRequests     LaborRequest[]     @relation("LaborRequests") // For LaborRequest.company
  contracts         Contract[]         @relation("CompanyContracts") // For Contract.company
  rentalsFrom       WorkerRental[]     @relation("RentalFrom") // For WorkerRental.fromCompany
  rentalsTo         WorkerRental[]     @relation("RentalTo") // For WorkerRental.toCompany
  clientProjects    Project[]          @relation("ClientProjects") // For Project.client
  projects          Project[]          @relation("CompanyProjects")
  tasks             Task[]             @relation("CompanyTasks")
  qualityChecklists QualityChecklist[] @relation("CompanyQualityChecklists")
  qualitySteps      QualityStep[]      @relation("CompanyQualitySteps")
  photos            Photo[]            @relation("CompanyPhotos")
  documents         Document[]         @relation("CompanyDocuments")

  // Production module relations
  equipment Equipment[]
  materials Material[]

  // Budget module relations
  incomes      Income[]      @relation("CompanyIncomes")
  expenses     Expense[]     @relation("CompanyExpenses")
  assets       Asset[]       @relation("CompanyAssets")
  budgets      Budget[]      @relation("CompanyBudgets")
  transactions Transaction[] @relation("CompanyTransactions")
  budgetGoals  BudgetGoal[]  @relation("CompanyBudgetGoals")

  // Sales module relations
  deals      Deal[]
  activities Activity[]
  quotations Quotation[]

  // Time management relations
  timeEntries            TimeEntry[]
  workLocations          WorkLocation[]
  timesheets             Timesheet[]
  workAssignments        WorkAssignment[]
  taskSuggestions        TaskSuggestion[]
  payrollReports         PayrollReport[]
  clientAccesses         ClientAccess[]
  timeManagementSettings TimeManagementSettings?

  // HR module relations
  benefits    Benefit[]
  trainings   Training[]
  hrDocuments HrDocument[]

  // Subscription relation
  subscriptions Subscription[]

  UserCompany UserCompany[]

  CommunicationEndpoint CommunicationEndpoint[]

  JobRequest JobRequest[]

  CompanyReview CompanyReview[]

  // Company join requests and invitations
  joinRequests CompanyJoinRequest[]
  invitations  CompanyInvitation[]

  Employee Employee[]

  Department Department[]

  // Subscription relation is defined at line 33

  // Client and supplier relationships
  clientRelationships   ClientRelationship[]   @relation("ClientCompanies")
  providerRelationships ClientRelationship[]   @relation("ProviderCompanies")
  buyerRelationships    SupplierRelationship[] @relation("BuyerCompanies")
  supplierRelationships SupplierRelationship[] @relation("SupplierCompanies")

  // Transition tracking
  becameUserAt         DateTime? // When this company became a platform user
  previousTransactions Json? // History of transactions before becoming a user
  transition           CompanyTransition?

  // Recruitment relations
  jobPostings          JobPosting[]
  assessments          Assessment[]
  emailTemplates       EmailTemplate[]
  recruitmentAnalytics RecruitmentAnalytics[]

  // Calendar relations
  calendarEvents   CalendarEvent[]
  calendarSettings CalendarSettings?

  // Map and location tracking relations
  gpsDevices      GpsDevice[]
  trackableAssets TrackableAsset[]
  mapLayers       MapLayer[]
}

model WorkforceNeed {
  id           Int           @id @default(autoincrement())
  companyId    Int
  company      Company       @relation(fields: [companyId], references: [id])
  skillsNeeded String[]
  quantity     Int
  duration     String
  budget       Float
  priority     Int
  status       RequestStatus
  aiMatches    Json? // Stores AI matching suggestions
}

model FinancialHealth {
  id             Int       @id @default(autoincrement())
  companyId      Int       @unique
  company        Company   @relation(fields: [companyId], references: [id])
  paymentHistory Float // Score 0-10
  stabilityIndex Float // Score 0-10
  riskLevel      RiskLevel
  lastAssessment DateTime
}

// Model to track client relationships between companies
model ClientRelationship {
  id                Int       @id @default(autoincrement())
  clientCompanyId   Int
  clientCompany     Company   @relation("ClientCompanies", fields: [clientCompanyId], references: [id])
  providerCompanyId Int
  providerCompany   Company   @relation("ProviderCompanies", fields: [providerCompanyId], references: [id])
  status            String // Active, Inactive, Pending, etc.
  startDate         DateTime  @default(now())
  endDate           DateTime?
  contractValue     Float?
  notes             String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Metrics
  lifetimeValue       Float? // Total value of all transactions
  lastTransactionDate DateTime?
  transactionCount    Int? // Number of transactions

  @@unique([clientCompanyId, providerCompanyId])
}

// Model to track supplier relationships between companies
model SupplierRelationship {
  id                Int       @id @default(autoincrement())
  buyerCompanyId    Int
  buyerCompany      Company   @relation("BuyerCompanies", fields: [buyerCompanyId], references: [id])
  supplierCompanyId Int
  supplierCompany   Company   @relation("SupplierCompanies", fields: [supplierCompanyId], references: [id])
  status            String // Active, Inactive, Pending, etc.
  startDate         DateTime  @default(now())
  endDate           DateTime?
  contractValue     Float?
  notes             String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Metrics
  lifetimeValue       Float? // Total value of all transactions
  lastTransactionDate DateTime?
  transactionCount    Int? // Number of transactions
  reliabilityScore    Float? // Score 0-10
  qualityScore        Float? // Score 0-10

  @@unique([buyerCompanyId, supplierCompanyId])
}

// Model to track the transition from client/supplier to platform user
model CompanyTransition {
  id               Int         @id @default(autoincrement())
  companyId        Int         @unique
  company          Company     @relation(fields: [companyId], references: [id])
  userId           Int?
  user             User?       @relation(fields: [userId], references: [id])
  previousType     CompanyType // What type the company was before transition
  transitionDate   DateTime    @default(now())
  transitionReason String?
  previousData     Json? // Snapshot of important data before transition
  notes            String?
  createdAt        DateTime    @default(now())
  updatedAt        DateTime    @updatedAt
}

// Content from 05-product.prisma
model ProjectProduct {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  projectId   Int?
  project     Project? @relation("ProjectProducts", fields: [projectId], references: [id])
}

model Project {
  id            Int           @id @default(autoincrement())
  name          String
  description   String?
  status        ProjectStatus
  startDate     DateTime
  endDate       DateTime?
  companyId     Int
  clientId      Int
  projectLeadId Int
  budget        Float
  barcode       String        @unique
  qrCode        String        @unique

  // Location fields
  latitude  Float?
  longitude Float?
  address   String?

  // Add relationships
  company         Company  @relation("CompanyProjects", fields: [companyId], references: [id])
  client          Company  @relation("ClientProjects", fields: [clientId], references: [id])
  projectLead     Worker   @relation("ProjectLeads", fields: [projectLeadId], references: [id])
  assignedWorkers Worker[] @relation("ProjectWorkers") // Many-to-many relationship with workers

  // Existing relationships
  tasks        Task[]
  documents    Document[]
  qcChecklists QualityChecklist[]
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt

  Worker Worker[] @relation("LedProjects")

  products ProjectProduct[] @relation("ProjectProducts")
  stages   Stage[]

  // Time management relations
  timeEntries     TimeEntry[]
  workAssignments WorkAssignment[]
  taskSuggestions TaskSuggestion[]
  clientAccesses  ClientAccess[]

  // Production module relations
  photoEvidences       PhotoEvidence[]
  projectDocuments     ProjectDocument[]
  timelineEvents       ProjectTimelineEvent[]
  resourceAllocations  ResourceAllocation[]
  productionReports    ProductionReport[]
  materialTransactions MaterialTransaction[]

  // Calendar relations
  calendarEvents CalendarEvent[]

  // Map and location tracking relations
  workerLocations  WorkerLocation[]           @relation("WorkerProjectLocations")
  assetAssignments TrackableAssetAssignment[]
}

model Task {
  id          Int           @id @default(autoincrement())
  projectId   Int
  companyId   Int // Add company relation
  name        String
  description String?
  status      TaskStatus
  assignedTo  Int?
  startDate   DateTime?
  endDate     DateTime?
  priority    Priority
  qrCode      String        @unique
  barcode     String        @unique
  documents   Document[]
  qcSteps     QualityStep[]

  company Company @relation("CompanyTasks", fields: [companyId], references: [id])
  project Project @relation(fields: [projectId], references: [id])
  worker  Worker? @relation("AssignedTasks", fields: [assignedTo], references: [id])

  // Map and location tracking relations
  workerLocations WorkerLocation[] @relation("WorkerTaskLocations")

  // Task dependencies
  dependsOn      TaskDependency[] @relation("DependentTask")
  dependentTasks TaskDependency[] @relation("ParentTask")

  // Task assignments
  assignments TaskAssignment[]

  // Resource allocations
  resourceAllocations ResourceAllocation[]

  // Photo evidence
  photoEvidences PhotoEvidence[]

  // Time entries
  timeEntries TimeEntry[]

  // Work assignments
  workAssignments WorkAssignment[]

  // Calendar relations
  calendarEvents CalendarEvent[]
}

model QualityChecklist {
  id          Int                @id @default(autoincrement())
  projectId   Int
  companyId   Int // Add company relation
  name        String
  steps       QualityStep[]
  items       QualityCheckItem[]
  createdById Int
  createdBy   User               @relation("ChecklistCreator", fields: [createdById], references: [id])
  checkedById Int?
  checkedBy   User?              @relation("ChecklistReviewer", fields: [checkedById], references: [id])

  company Company @relation("CompanyQualityChecklists", fields: [companyId], references: [id])
  project Project @relation(fields: [projectId], references: [id])
}

model QualityStep {
  id          Int     @id @default(autoincrement())
  taskId      Int
  checklistId Int
  companyId   Int // Add company relation
  name        String
  description String?
  required    Boolean @default(true)
  completed   Boolean @default(false)
  photos      Photo[]

  company   Company          @relation("CompanyQualitySteps", fields: [companyId], references: [id])
  task      Task             @relation(fields: [taskId], references: [id])
  checklist QualityChecklist @relation(fields: [checklistId], references: [id])
}

model Photo {
  id        Int      @id @default(autoincrement())
  stepId    Int
  companyId Int // Add company relation
  url       String
  timestamp DateTime @default(now())

  company Company     @relation("CompanyPhotos", fields: [companyId], references: [id])
  step    QualityStep @relation(fields: [stepId], references: [id])
}

model Document {
  id          Int          @id @default(autoincrement())
  projectId   Int?
  taskId      Int?
  companyId   Int // Add company relation
  name        String
  url         String
  type        DocumentType
  description String?
  size        Int          @default(0)
  mimeType    String?
  uploadedBy  Int
  category    String?
  module      String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  company  Company    @relation("CompanyDocuments", fields: [companyId], references: [id])
  project  Project?   @relation(fields: [projectId], references: [id])
  task     Task?      @relation(fields: [taskId], references: [id])
  Employee Employee[] @relation("EmployeeDocuments")
}

model Stage {
  id          Int     @id @default(autoincrement())
  name        String
  description String?
  project     Project @relation(fields: [projectId], references: [id])
  projectId   Int

  // Add other stage-related fields as needed
}

// Content from 03-hr.prisma
// HR Module Schema

// Worker-related models (existing in 03-hr.prisma)
model Worker {
  id               Int                 @id @default(autoincrement())
  firstName        String
  lastName         String
  email            String              @unique
  phone            String?
  address          String?
  skills           Skill[]
  certifications   Certification[]
  availability     WorkerAvailability? @relation("WorkerAvailabilityRelation") // Changed to optional
  rating           Float               @default(0)
  status           WorkerStatus
  currentCompany   Company?            @relation(fields: [currentCompanyId], references: [id], name: "CurrentEmployer")
  currentCompanyId Int?
  rentalHistory    WorkerRental[]
  feedbacks        Feedback[]
  contracts        Contract[]
  healthChecks     HealthCheck[]
  documents        WorkerDocument[]
  salary           SalaryInfo?
  createdAt        DateTime            @default(now())
  updatedAt        DateTime            @updatedAt

  // Added opposite relation fields
  laborRequestId   Int? // Foreign key for LaborRequest
  laborRequest     LaborRequest? @relation("MatchedWorkers", fields: [laborRequestId], references: [id])
  ledProjects      Project[]     @relation("LedProjects") // For Project.projectLead
  assignedTasks    Task[]        @relation("AssignedTasks") // For Task.worker
  assignedProjects Project[]     @relation("ProjectWorkers")

  Project Project[] @relation("ProjectLeads")

  WorkerProfile WorkerProfile[]

  RentalCompany RentalCompany[]
}

model LaborRequest {
  id             Int           @id @default(autoincrement())
  companyId      Int
  company        Company       @relation("LaborRequests", fields: [companyId], references: [id])
  requirements   Json
  duration       Duration
  quantity       Int
  status         RequestStatus
  matchedWorkers Worker[]      @relation("MatchedWorkers")
  aiScore        Float?
}

model Contract {
  id           Int               @id @default(autoincrement())
  workerId     Int
  worker       Worker            @relation(fields: [workerId], references: [id])
  companyId    Int
  company      Company           @relation("CompanyContracts", fields: [companyId], references: [id])
  terms        Json
  duration     Duration
  status       ContractStatus
  timeTracking TimeTrackingLog[]
}

model Skill {
  id         Int      @id @default(autoincrement())
  name       String
  category   String
  level      Int // 1-5 rating
  workers    Worker[]
  verified   Boolean  @default(false)
  verifiedBy String? // Company that verified the skill
}

model Certification {
  id         Int      @id @default(autoincrement())
  name       String
  issuer     String
  validUntil DateTime
  document   String // URL to stored document
  verified   Boolean
  worker     Worker   @relation(fields: [workerId], references: [id])
  workerId   Int
}

model WorkerRental {
  id            Int          @id @default(autoincrement())
  worker        Worker       @relation(fields: [workerId], references: [id])
  workerId      Int
  fromCompany   Company      @relation("RentalFrom", fields: [fromCompanyId], references: [id])
  fromCompanyId Int
  toCompany     Company      @relation("RentalTo", fields: [toCompanyId], references: [id])
  toCompanyId   Int
  startDate     DateTime
  endDate       DateTime
  status        RentalStatus
  hourlyRate    Float
  mediationFee  Float
  contract      String? // URL to contract document
}

model WorkerDocument {
  id         Int          @id @default(autoincrement())
  worker     Worker       @relation(fields: [workerId], references: [id])
  workerId   Int
  type       DocumentType
  url        String
  verified   Boolean      @default(false)
  uploadedAt DateTime     @default(now())
}

model WorkerAvailability {
  id          Int    @id @default(autoincrement())
  worker      Worker @relation("WorkerAvailabilityRelation", fields: [workerId], references: [id])
  workerId    Int    @unique
  schedule    Json
  notice      Int
  minHours    Int
  maxHours    Int
  preferences Json?
}

model Feedback {
  id        Int      @id @default(autoincrement())
  workerId  Int
  worker    Worker   @relation(fields: [workerId], references: [id])
  rating    Float
  comment   String?
  createdAt DateTime @default(now())
}

model HealthCheck {
  id       Int      @id @default(autoincrement())
  workerId Int
  worker   Worker   @relation(fields: [workerId], references: [id])
  date     DateTime
  status   String
  notes    String?
}

model SalaryInfo {
  id            Int      @id @default(autoincrement())
  workerId      Int      @unique
  worker        Worker   @relation(fields: [workerId], references: [id])
  amount        Float
  currency      String
  effectiveDate DateTime
}

model TimeTrackingLog {
  id         Int       @id @default(autoincrement())
  contractId Int
  contract   Contract  @relation(fields: [contractId], references: [id])
  startTime  DateTime
  endTime    DateTime?
  duration   Float?
  notes      String?
}

// Employee model
model Employee {
  id               Int            @id @default(autoincrement())
  userId           Int?
  user             User?          @relation(fields: [userId], references: [id])
  companyId        Int
  company          Company        @relation(fields: [companyId], references: [id])
  position         String
  department       String
  startDate        DateTime
  endDate          DateTime?
  salary           Float
  status           EmployeeStatus @default(ACTIVE)
  employmentType   EmploymentType @default(FULL_TIME)
  emergencyContact String?
  emergencyPhone   String?
  managerUserId    Int?
  manager          User?          @relation("EmployeeManager", fields: [managerUserId], references: [id])
  managedEmployees Employee[]     @relation("ManagerEmployees")
  managerId        Int?
  managerEmployee  Employee?      @relation("ManagerEmployees", fields: [managerId], references: [id])
  createdBy        Int?
  createdByUser    User?          @relation("EmployeeCreator", fields: [createdBy], references: [id])
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  // Relations
  leaveRequests      LeaveRequest[]      @relation("EmployeeLeaveRequests")
  payrollHistory     Payroll[]           @relation("EmployeePayroll")
  attendanceRecords  Attendance[]        @relation("EmployeeAttendance")
  shifts             Shift[]             @relation("EmployeeShifts")
  benefits           EmployeeBenefit[]   @relation("EmployeeBenefits")
  bonuses            Bonus[]             @relation("EmployeeBonuses")
  documents          Document[]          @relation("EmployeeDocuments")
  performanceReviews PerformanceReview[] @relation("EmployeePerformanceReviews")
  trainings          Training[]          @relation("EmployeeTrainings")
  careerPath         CareerPath?         @relation("EmployeeCareerPath")

  // Basic model relations
  employeeBenefitsBasic EmployeeBenefitBasic[] @relation("EmployeeBenefits")
  bonusesBasic          BonusBasic[]           @relation("EmployeeBonuses")
  trainingsBasic        TrainingBasic[]        @relation("EmployeeTrainings")
  careerPathBasic       CareerPathBasic?       @relation("EmployeeCareerPath")
  employeeTrainings     EmployeeTraining[]     @relation("EmployeeTrainings")
  employeeDocuments     EmployeeDocument[]     @relation("EmployeeDocuments")

  // Department relation
  managedDepartments Department[] @relation("DepartmentManager")

  LeaveBalance LeaveBalance[]
}

model Department {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  companyId   Int
  company     Company   @relation(fields: [companyId], references: [id])
  managerId   Int?
  manager     Employee? @relation("DepartmentManager", fields: [managerId], references: [id])
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  positions Position[]
}

model Position {
  id           Int        @id @default(autoincrement())
  title        String
  description  String?
  departmentId Int
  department   Department @relation(fields: [departmentId], references: [id])
  minSalary    Float?
  maxSalary    Float?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
}

model LeaveRequest {
  id         Int         @id @default(autoincrement())
  employeeId Int
  employee   Employee    @relation("EmployeeLeaveRequests", fields: [employeeId], references: [id])
  startDate  DateTime
  endDate    DateTime
  leaveType  LeaveType
  reason     String?
  status     LeaveStatus @default(PENDING)
  approvedBy Int?
  approver   User?       @relation(fields: [approvedBy], references: [id])
  approvedAt DateTime?
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
}

model LeaveBalance {
  id         Int       @id @default(autoincrement())
  employeeId Int
  employee   Employee  @relation(fields: [employeeId], references: [id])
  leaveType  LeaveType
  balance    Float
  year       Int
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@unique([employeeId, leaveType, year])
}

model Attendance {
  id         Int       @id @default(autoincrement())
  employeeId Int
  employee   Employee  @relation("EmployeeAttendance", fields: [employeeId], references: [id])
  date       DateTime
  clockIn    DateTime
  clockOut   DateTime?
  status     String // PRESENT, ABSENT, LATE, HALF_DAY
  notes      String?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
}

model Shift {
  id         Int      @id @default(autoincrement())
  name       String
  startTime  DateTime
  endTime    DateTime
  employeeId Int
  employee   Employee @relation("EmployeeShifts", fields: [employeeId], references: [id])
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model Payroll {
  id          Int           @id @default(autoincrement())
  employeeId  Int
  employee    Employee      @relation("EmployeePayroll", fields: [employeeId], references: [id])
  periodStart DateTime
  periodEnd   DateTime
  basicSalary Float
  allowances  Float
  deductions  Float
  taxes       Float
  netSalary   Float
  status      PayrollStatus @default(DRAFT)
  processedBy Int?
  processor   User?         @relation(fields: [processedBy], references: [id])
  processedAt DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
}

model EmployeeBenefitBasic {
  id           Int         @id @default(autoincrement())
  employeeId   Int
  employee     Employee    @relation("EmployeeBenefits", fields: [employeeId], references: [id])
  benefitType  BenefitType
  provider     String?
  policyNumber String?
  startDate    DateTime
  endDate      DateTime?
  amount       Float?
  description  String?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
}

model BonusBasic {
  id         Int       @id @default(autoincrement())
  employeeId Int
  employee   Employee  @relation("EmployeeBonuses", fields: [employeeId], references: [id])
  bonusType  BonusType
  amount     Float
  date       DateTime
  reason     String?
  approvedBy Int?
  approver   User?     @relation(fields: [approvedBy], references: [id])
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
}

model TrainingBasic {
  id             Int       @id @default(autoincrement())
  name           String
  description    String?
  employeeId     Int
  employee       Employee  @relation("EmployeeTrainings", fields: [employeeId], references: [id])
  startDate      DateTime
  endDate        DateTime?
  status         String // SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED
  provider       String?
  cost           Float?
  certificateUrl String?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
}

model CareerPathBasic {
  id             Int      @id @default(autoincrement())
  employeeId     Int      @unique
  employee       Employee @relation("EmployeeCareerPath", fields: [employeeId], references: [id])
  currentLevel   String
  targetPosition String?
  skills         String? // Comma-separated skills to develop
  timeline       String? // Career development timeline
  mentorId       Int?
  mentor         User?    @relation(fields: [mentorId], references: [id])
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

// Benefits model
model Benefit {
  id                   Int           @id @default(autoincrement())
  name                 String
  description          String?
  type                 BenefitType
  provider             String?
  policyNumber         String?
  coverage             Float?
  premium              Float?
  employerContribution Float?
  employeeContribution Float?
  startDate            DateTime
  endDate              DateTime?
  status               BenefitStatus @default(ACTIVE)
  documents            String[] // URLs to benefit documents
  companyId            Int
  company              Company       @relation(fields: [companyId], references: [id])
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt

  // Relations
  employeeBenefits EmployeeBenefit[] @relation("BenefitRelation")
}

// Employee Benefit model (join table)
model EmployeeBenefit {
  id                   Int           @id @default(autoincrement())
  employeeId           Int
  employee             Employee      @relation("EmployeeBenefits", fields: [employeeId], references: [id])
  benefitId            Int
  benefit              Benefit       @relation("BenefitRelation", fields: [benefitId], references: [id])
  enrollmentDate       DateTime      @default(now())
  effectiveDate        DateTime
  coverageAmount       Float?
  premium              Float?
  employeeContribution Float?
  dependents           Json? // Information about covered dependents
  notes                String?
  status               BenefitStatus @default(ACTIVE)
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt

  @@unique([employeeId, benefitId])
}

// Bonus model
model Bonus {
  id          Int       @id @default(autoincrement())
  employeeId  Int
  employee    Employee  @relation("EmployeeBonuses", fields: [employeeId], references: [id])
  type        BonusType
  amount      Float
  currency    String    @default("USD")
  description String?
  awardDate   DateTime  @default(now())
  paymentDate DateTime?
  isPaid      Boolean   @default(false)
  approvedBy  Int?
  approver    User?     @relation(fields: [approvedBy], references: [id])
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// Performance Review model
model PerformanceReview {
  id               Int               @id @default(autoincrement())
  employeeId       Int
  employee         Employee          @relation("EmployeePerformanceReviews", fields: [employeeId], references: [id])
  reviewerId       Int
  reviewer         User              @relation(fields: [reviewerId], references: [id])
  reviewDate       DateTime
  rating           PerformanceRating
  strengths        String?
  weaknesses       String?
  goals            String?
  feedback         String?
  employeeComments String?
  isAcknowledged   Boolean           @default(false)
  acknowledgedAt   DateTime?
  nextReviewDate   DateTime?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
}

// Training model
model Training {
  id          Int            @id @default(autoincrement())
  title       String
  description String?
  type        TrainingType
  provider    String?
  location    String?
  startDate   DateTime
  endDate     DateTime?
  duration    Int? // Duration in hours
  cost        Float?
  materials   String[] // URLs to training materials
  status      TrainingStatus @default(SCHEDULED)
  companyId   Int
  company     Company        @relation(fields: [companyId], references: [id])
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  employeeTrainings EmployeeTraining[] @relation("TrainingRelation")
  employees         Employee[]         @relation("EmployeeTrainings")
}

// Employee Training model (join table)
model EmployeeTraining {
  id             Int            @id @default(autoincrement())
  employeeId     Int
  employee       Employee       @relation("EmployeeTrainings", fields: [employeeId], references: [id])
  trainingId     Int
  training       Training       @relation("TrainingRelation", fields: [trainingId], references: [id])
  enrollmentDate DateTime       @default(now())
  completionDate DateTime?
  status         TrainingStatus @default(SCHEDULED)
  score          Float?
  certificate    String? // URL to certificate
  feedback       String?
  notes          String?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  @@unique([employeeId, trainingId])
}

// Career Path model
model CareerPath {
  id              Int       @id @default(autoincrement())
  employeeId      Int       @unique
  employee        Employee  @relation("EmployeeCareerPath", fields: [employeeId], references: [id])
  currentPosition String
  targetPosition  String?
  skills          String? // Comma-separated skills to develop
  timeline        String? // Career development timeline
  mentorId        Int?
  mentor          User?     @relation(fields: [mentorId], references: [id])
  developmentPlan String?
  progressNotes   String?
  lastReviewDate  DateTime?
  nextReviewDate  DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

// Document model
model HrDocument {
  id             Int              @id @default(autoincrement())
  title          String
  description    String?
  category       DocumentCategory
  fileUrl        String // URL to the document file
  fileType       String // File extension or MIME type
  fileSize       Int? // Size in bytes
  isConfidential Boolean          @default(false)
  expiryDate     DateTime?
  uploadedBy     Int
  uploader       User             @relation(fields: [uploadedBy], references: [id])
  companyId      Int
  company        Company          @relation(fields: [companyId], references: [id])
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt

  // Relations
  employeeDocuments EmployeeDocument[] @relation("DocumentRelation")
}

// Employee Document model (join table)
model EmployeeDocument {
  id             Int        @id @default(autoincrement())
  employeeId     Int
  employee       Employee   @relation("EmployeeDocuments", fields: [employeeId], references: [id])
  documentId     Int
  document       HrDocument @relation("DocumentRelation", fields: [documentId], references: [id])
  accessLevel    String     @default("VIEW") // VIEW, EDIT, etc.
  isAcknowledged Boolean    @default(false)
  acknowledgedAt DateTime?
  notes          String?
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt

  @@unique([employeeId, documentId])
}

// Content from 04-core.prisma
model BusinessRule {
  id        Int          @id @default(autoincrement())
  name      String
  type      RuleType
  condition Json // Structured condition logic
  action    Json // Action to take when condition is met
  severity  RuleSeverity
  priority  Int
  isActive  Boolean      @default(true)
  metadata  Json? // Additional context
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
}

model BusinessStrategy {
  id          Int      @id @default(autoincrement())
  name        String
  description String
  parameters  Json // Strategy configuration
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model AiDecisionLog {
  id         Int           @id @default(autoincrement())
  module     String // Which module triggered this decision
  action     String // What action was taken
  context    Json // Decision context
  rules      Json // Applied business rules
  suggestion String? // AI suggestion if any
  severity   RuleSeverity?
  timestamp  DateTime      @default(now())
}

model SystemIntegration {
  id           Int      @id @default(autoincrement())
  moduleName   String   @unique
  isEnabled    Boolean  @default(true)
  config       Json // Module-specific configuration
  dependencies String[] // Other modules this depends on
}

model Contact {
  id        Int     @id @default(autoincrement())
  name      String
  email     String
  phone     String?
  companyId Int
  company   Company @relation(fields: [companyId], references: [id])
}

model Agreement {
  id        Int     @id @default(autoincrement())
  title     String
  content   String
  companyId Int
  company   Company @relation(fields: [companyId], references: [id])
}

model RiskAssessment {
  id          Int      @id @default(autoincrement())
  companyId   Int      @unique
  company     Company  @relation(fields: [companyId], references: [id])
  score       Float
  details     Json
  lastUpdated DateTime @default(now())
}

model PageAccessRight {
  id        Int      @id @default(autoincrement())
  path      String // Route path
  module    String // Module name (e.g., 'core', 'budget')
  role      Role // User role
  canAccess Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([path, role], name: "path_role")
}

// Content from 06-accounting.prisma
model Account {
  id              Int            @id @default(autoincrement())
  name            String
  code            String         @unique
  type            AccountType
  balance         Float          @default(0)
  parentAccountId Int?
  parentAccount   Account?       @relation("AccountHierarchy", fields: [parentAccountId], references: [id])
  subAccounts     Account[]      @relation("AccountHierarchy")
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  debitEntries    JournalEntry[] @relation("DebitEntries") // Replaces journalEntries
  creditEntries   JournalEntry[] @relation("CreditEntries") // Replaces journalEntries
  metadata        Json?
}

model JournalEntry {
  id              Int         @id @default(autoincrement())
  date            DateTime
  description     String?
  amount          Float
  debitAccountId  Int
  creditAccountId Int
  debitAccount    Account     @relation("DebitEntries", fields: [debitAccountId], references: [id])
  creditAccount   Account     @relation("CreditEntries", fields: [creditAccountId], references: [id])
  status          EntryStatus
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  metadata        Json?
}

// Content from 07-budget.prisma
model Income {
  id            Int           @id @default(autoincrement())
  userId        Int
  user          User          @relation("UserIncomes", fields: [userId], references: [id])
  companyId     Int?
  company       Company?      @relation("CompanyIncomes", fields: [companyId], references: [id])
  name          String
  description   String?
  amount        Float
  paymentMethod PaymentMethod
  source        IncomeSource
  interval      IntervalType
  startDate     DateTime
  endDate       DateTime
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  paid          Boolean       @default(false)
  barcode       String?       @unique
  qrCode        String?       @unique
  category      String?
  notes         String?
  budgetId      Int?
  budget        Budget?       @relation("BudgetIncomes", fields: [budgetId], references: [id])
}

model Expense {
  id            Int           @id @default(autoincrement())
  userId        Int
  user          User          @relation("UserExpenses", fields: [userId], references: [id])
  companyId     Int?
  company       Company?      @relation("CompanyExpenses", fields: [companyId], references: [id])
  name          String
  description   String?
  amount        Float
  paymentMethod PaymentMethod
  source        ExpenseSource
  interval      IntervalType
  startDate     DateTime
  endDate       DateTime
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  paid          Boolean       @default(false)
  barcode       String?       @unique
  qrCode        String?       @unique
  category      String?
  notes         String?
  budgetId      Int?
  budget        Budget?       @relation("BudgetExpenses", fields: [budgetId], references: [id])
}

model Asset {
  id            Int         @id @default(autoincrement())
  userId        Int
  user          User        @relation("UserAssets", fields: [userId], references: [id])
  companyId     Int?
  company       Company?    @relation("CompanyAssets", fields: [companyId], references: [id])
  method        AssetMethod
  customName    String?
  initialAmount Float       @default(0)
  currentAmount Float       @default(0)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  barcode       String?     @unique
  qrCode        String?     @unique
  notes         String?
}

model Budget {
  id          Int        @id @default(autoincrement())
  userId      Int
  user        User       @relation("UserBudgets", fields: [userId], references: [id])
  companyId   Int?
  company     Company?   @relation("CompanyBudgets", fields: [companyId], references: [id])
  name        String
  description String?
  startDate   DateTime
  endDate     DateTime
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  incomes     Income[]   @relation("BudgetIncomes")
  expenses    Expense[]  @relation("BudgetExpenses")
  isActive    Boolean    @default(true)
  type        BudgetType @default(PERSONAL)
}

model Transaction {
  id          Int               @id @default(autoincrement())
  userId      Int
  user        User              @relation("UserTransactions", fields: [userId], references: [id])
  companyId   Int?
  company     Company?          @relation("CompanyTransactions", fields: [companyId], references: [id])
  type        TransactionType
  description String
  amount      Float
  category    String
  date        DateTime
  status      TransactionStatus @default(COMPLETED)
  notes       String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
}

model BudgetGoal {
  id            Int        @id @default(autoincrement())
  userId        Int
  user          User       @relation("UserBudgetGoals", fields: [userId], references: [id])
  companyId     Int?
  company       Company?   @relation("CompanyBudgetGoals", fields: [companyId], references: [id])
  name          String
  description   String?
  targetAmount  Float
  currentAmount Float      @default(0)
  startDate     DateTime
  endDate       DateTime
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt
  type          GoalType   @default(SAVING)
  status        GoalStatus @default(IN_PROGRESS)
}

// Content from 08-communication.prisma
// Email models
model EmailAccount {
  id        Int           @id @default(autoincrement())
  userId    Int
  email     String
  name      String
  provider  EmailProvider
  isDefault Boolean       @default(false)
  isActive  Boolean       @default(true)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Relationships
  user     User           @relation(fields: [userId], references: [id])
  messages EmailMessage[]
  folders  EmailFolder[]
}

model EmailMessage {
  id             Int           @id @default(autoincrement())
  accountId      Int
  folderId       Int
  conversationId String?
  from           String
  to             String[]
  cc             String[]
  bcc            String[]
  subject        String
  body           String        @db.Text
  isHtml         Boolean       @default(false)
  status         EmailStatus   @default(UNREAD)
  priority       EmailPriority @default(NORMAL)
  hasAttachments Boolean       @default(false)
  receivedAt     DateTime
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  // Relationships
  account     EmailAccount      @relation(fields: [accountId], references: [id])
  folder      EmailFolder       @relation(fields: [folderId], references: [id])
  attachments EmailAttachment[]
}

model EmailAttachment {
  id        Int      @id @default(autoincrement())
  messageId Int
  name      String
  type      String
  size      Int
  url       String
  createdAt DateTime @default(now())

  // Relationships
  message EmailMessage @relation(fields: [messageId], references: [id])
}

model EmailFolder {
  id        Int      @id @default(autoincrement())
  accountId Int
  name      String
  type      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  account  EmailAccount   @relation(fields: [accountId], references: [id])
  messages EmailMessage[]
}

// Chat models
model ChatConversation {
  id        Int                  @id @default(autoincrement())
  name      String?
  type      ChatConversationType
  createdAt DateTime             @default(now())
  updatedAt DateTime             @updatedAt

  // Relationships
  participants ChatParticipant[]
  messages     ChatMessage[]
}

model ChatParticipant {
  id             Int       @id @default(autoincrement())
  conversationId Int
  userId         Int
  joinedAt       DateTime  @default(now())
  leftAt         DateTime?

  // Relationships
  conversation ChatConversation @relation(fields: [conversationId], references: [id])
  user         User             @relation(fields: [userId], references: [id])
}

model ChatMessage {
  id             Int               @id @default(autoincrement())
  conversationId Int
  senderId       Int
  content        String            @db.Text
  status         ChatMessageStatus @default(SENT)
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt

  // Relationships
  conversation ChatConversation @relation(fields: [conversationId], references: [id])
  sender       User             @relation(fields: [senderId], references: [id])
}

// Endpoint models
model CommunicationEndpoint {
  id          Int            @id @default(autoincrement())
  userId      Int
  companyId   Int
  name        String
  description String?        @db.Text
  path        String
  method      EndpointMethod
  apiKey      String         @unique
  status      EndpointStatus @default(ACTIVE)
  fields      Json // Array of field definitions
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relationships
  user     User              @relation(fields: [userId], references: [id])
  company  Company           @relation(fields: [companyId], references: [id])
  requests EndpointRequest[]
}

model EndpointRequest {
  id             Int      @id @default(autoincrement())
  endpointId     Int
  ipAddress      String
  userAgent      String?
  requestData    Json
  responseData   Json?
  statusCode     Int
  processingTime Int
  isRead         Boolean  @default(false)
  createdAt      DateTime @default(now())

  // Relationships
  endpoint CommunicationEndpoint @relation(fields: [endpointId], references: [id])
}

// Content from 09-subscription.prisma
// Legacy subscription model
model SubscriptionPackage {
  id             Int                  @id @default(autoincrement())
  name           String               @unique
  description    String?
  price          Float
  durationInDays Int
  createdAt      DateTime             @default(now())
  updatedAt      DateTime             @updatedAt
  subscriptions  LegacySubscription[]
}

// Legacy subscription model - renamed to avoid conflicts
model LegacySubscription {
  id        Int                 @id @default(autoincrement())
  userId    Int
  user      User                @relation("LegacySubscriptions", fields: [userId], references: [id])
  packageId Int
  package   SubscriptionPackage @relation(fields: [packageId], references: [id])
  startDate DateTime            @default(now())
  endDate   DateTime
  price     Float
  isActive  Boolean             @default(true)
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt

  @@map("Subscription") // Maps to the existing Subscription table
}

// New subscription model
model SubscriptionPlan {
  id                Int                  @id @default(autoincrement())
  name              String
  description       String
  type              SubscriptionPlanType
  monthlyPrice      Float
  yearlyPrice       Float
  maxUsers          Int
  additionalUserFee Float
  features          String[] // JSON array of features
  modules           String[] // JSON array of included modules
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  subscriptions     Subscription[]
}

// New subscription model
model Subscription {
  id                   Int                @id @default(autoincrement())
  userId               Int
  user                 User               @relation("Subscriptions", fields: [userId], references: [id])
  companyId            Int?
  company              Company?           @relation(fields: [companyId], references: [id])
  planId               Int
  plan                 SubscriptionPlan   @relation(fields: [planId], references: [id])
  status               SubscriptionStatus
  currentUsers         Int                @default(1)
  startDate            DateTime           @default(now())
  endDate              DateTime?
  trialEndDate         DateTime?
  billingCycle         BillingCycle
  lastBillingDate      DateTime?
  nextBillingDate      DateTime?
  stripeCustomerId     String?
  stripeSubscriptionId String?
  cancelAtPeriodEnd    Boolean            @default(false)
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt
  payments             Payment[]

  @@map("NewSubscription") // Maps to a new table to avoid conflicts
}

model Payment {
  id              Int           @id @default(autoincrement())
  subscriptionId  Int?
  subscription    Subscription? @relation(fields: [subscriptionId], references: [id])
  amount          Float
  currency        String        @default("USD")
  status          String // "succeeded", "pending", "failed"
  stripePaymentId String?
  stripeInvoiceId String?
  paymentMethod   String? // "card", "bank_transfer", etc.
  paymentDate     DateTime      @default(now())
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Supporter payment fields
  isSupporterPayment Boolean @default(false)
  supporterUserId    Int?
  supporterUser      User?   @relation("SupporterPayments", fields: [supporterUserId], references: [id])
  packageType        String? // "Silver Supporter", "Gold Supporter", "Platinum Supporter"
  packageAmount      Float? // Amount of the supporter package
}

// Content from 10-sales.prisma
// Models
model Lead {
  id             Int        @id @default(autoincrement())
  firstName      String
  lastName       String
  email          String
  phone          String?
  company        String?
  jobTitle       String?
  status         LeadStatus @default(NEW)
  source         LeadSource
  notes          String?
  assignedToId   Int?
  assignedTo     User?      @relation(fields: [assignedToId], references: [id])
  score          Int? // Lead score (0-100)
  estimatedValue Float? // Potential deal value
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  convertedAt    DateTime?

  // Relations
  activities   Activity[]
  deals        Deal[]
  tags         LeadTag[]
  customFields LeadCustomField[]
  campaigns    CampaignLead[]
}

model LeadTag {
  id        Int      @id @default(autoincrement())
  leadId    Int
  lead      Lead     @relation(fields: [leadId], references: [id], onDelete: Cascade)
  name      String
  createdAt DateTime @default(now())

  @@unique([leadId, name])
}

model LeadCustomField {
  id        Int      @id @default(autoincrement())
  leadId    Int
  lead      Lead     @relation(fields: [leadId], references: [id], onDelete: Cascade)
  name      String
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([leadId, name])
}

model Deal {
  id                Int       @id @default(autoincrement())
  name              String
  description       String?
  value             Float
  currency          String    @default("USD")
  stage             DealStage
  type              DealType
  probability       Int // 0-100%
  expectedCloseDate DateTime?
  actualCloseDate   DateTime?
  leadId            Int?
  lead              Lead?     @relation(fields: [leadId], references: [id])
  companyId         Int?
  company           Company?  @relation(fields: [companyId], references: [id])
  assignedToId      Int?
  assignedTo        User?     @relation(fields: [assignedToId], references: [id])
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  activities Activity[]
  products   DealProduct[]
  quotations Quotation[]
  notes      DealNote[]
}

model DealProduct {
  id        Int      @id @default(autoincrement())
  dealId    Int
  deal      Deal     @relation(fields: [dealId], references: [id], onDelete: Cascade)
  productId Int
  product   Product  @relation(fields: [productId], references: [id])
  quantity  Int
  unitPrice Float
  discount  Float    @default(0)
  total     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([dealId, productId])
}

model DealNote {
  id          Int      @id @default(autoincrement())
  dealId      Int
  deal        Deal     @relation(fields: [dealId], references: [id], onDelete: Cascade)
  content     String
  createdById Int
  createdBy   User     @relation(fields: [createdById], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Activity {
  id           Int            @id @default(autoincrement())
  type         ActivityType
  subject      String
  description  String?
  status       ActivityStatus
  startDate    DateTime
  endDate      DateTime?
  location     String?
  leadId       Int?
  lead         Lead?          @relation(fields: [leadId], references: [id])
  dealId       Int?
  deal         Deal?          @relation(fields: [dealId], references: [id])
  companyId    Int?
  company      Company?       @relation(fields: [companyId], references: [id])
  assignedToId Int
  assignedTo   User           @relation(fields: [assignedToId], references: [id])
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  completedAt  DateTime?
  outcome      String? // Result of the activity

  // Calendar relations
  calendarEvents CalendarEvent[]
}

model Campaign {
  id              Int            @id @default(autoincrement())
  name            String
  description     String?
  type            CampaignType
  status          CampaignStatus
  budget          Float?
  actualCost      Float?
  startDate       DateTime
  endDate         DateTime?
  targetAudience  String?
  expectedRevenue Float?
  actualRevenue   Float?
  createdById     Int
  createdBy       User           @relation(fields: [createdById], references: [id])
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Relations
  leads   CampaignLead[]
  metrics CampaignMetric[]
}

model CampaignLead {
  id         Int      @id @default(autoincrement())
  campaignId Int
  campaign   Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  leadId     Int
  lead       Lead     @relation(fields: [leadId], references: [id])
  status     String // e.g., "sent", "opened", "clicked", "responded"
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([campaignId, leadId])
}

model CampaignMetric {
  id         Int      @id @default(autoincrement())
  campaignId Int
  campaign   Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  name       String // e.g., "opens", "clicks", "conversions"
  value      Float
  date       DateTime
  createdAt  DateTime @default(now())

  @@unique([campaignId, name, date])
}

// Product model for sales module
model Product {
  id          Int             @id @default(autoincrement())
  name        String
  description String?
  sku         String?         @unique
  category    ProductCategory
  price       Float
  currency    String          @default("USD")
  cost        Float?
  taxRate     Float           @default(0)
  isActive    Boolean         @default(true)
  imageUrl    String?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // Relations
  deals          DealProduct[]
  quotationItems QuotationItem[]
}

model Quotation {
  id              Int             @id @default(autoincrement())
  quotationNumber String          @unique
  title           String
  dealId          Int?
  deal            Deal?           @relation(fields: [dealId], references: [id])
  companyId       Int
  company         Company         @relation(fields: [companyId], references: [id])
  contactName     String?
  contactEmail    String?
  contactPhone    String?
  status          QuotationStatus @default(DRAFT)
  issueDate       DateTime
  expiryDate      DateTime
  subtotal        Float
  taxAmount       Float
  discountAmount  Float
  totalAmount     Float
  terms           String?
  notes           String?
  createdById     Int
  createdBy       User            @relation(fields: [createdById], references: [id])
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  sentAt          DateTime?
  viewedAt        DateTime?
  acceptedAt      DateTime?
  rejectedAt      DateTime?

  // Relations
  items QuotationItem[]
}

model QuotationItem {
  id          Int       @id @default(autoincrement())
  quotationId Int
  quotation   Quotation @relation(fields: [quotationId], references: [id], onDelete: Cascade)
  productId   Int?
  product     Product?  @relation(fields: [productId], references: [id])
  description String
  quantity    Int
  unitPrice   Float
  discount    Float     @default(0)
  taxRate     Float     @default(0)
  total       Float
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model SalesGoal {
  id           Int      @id @default(autoincrement())
  name         String
  description  String?
  targetAmount Float
  currency     String   @default("USD")
  startDate    DateTime
  endDate      DateTime
  assignedToId Int?
  assignedTo   User?    @relation(fields: [assignedToId], references: [id])
  goalType     String // e.g., "revenue", "deals_closed", "new_clients"
  progress     Float    @default(0)
  isAchieved   Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model SalesPipelineStage {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  order       Int
  probability Int // Default probability for this stage (0-100%)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Relations to add to existing models

// These relations will be added to the User model defined in 01-user.prisma
// model User {
//   // Sales module relations
//   assignedLeads     Lead[]
//   assignedDeals     Deal[]
//   salesActivities   Activity[]
//   createdCampaigns  Campaign[]
//   createdQuotations Quotation[]
//   dealNotes         DealNote[]
//   salesGoals        SalesGoal[]
// }

// These relations will be added to the Company model defined in 02-company.prisma
// model Company {
//   // Sales module relations
//   deals             Deal[]
//   salesActivities   Activity[]
//   quotations        Quotation[]
// }

// Content from 11-timemanagement.prisma
// Time Management Module Schema

// TimeEntry model to track work time
model TimeEntry {
  id          Int             @id @default(autoincrement())
  userId      Int
  user        User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  projectId   Int?
  project     Project?        @relation(fields: [projectId], references: [id], onDelete: SetNull)
  taskId      Int?
  task        Task?           @relation(fields: [taskId], references: [id], onDelete: SetNull)
  companyId   Int
  company     Company         @relation(fields: [companyId], references: [id], onDelete: Cascade)
  startTime   DateTime
  endTime     DateTime?
  duration    Int? // Duration in minutes
  description String?
  status      TimeEntryStatus @default(ACTIVE)
  approved    Boolean         @default(false)
  approvedBy  Int?
  approvedAt  DateTime?
  notes       String?

  // Location tracking
  startLocation   Location? @relation("StartLocation", fields: [startLocationId], references: [id])
  startLocationId Int?
  endLocation     Location? @relation("EndLocation", fields: [endLocationId], references: [id])
  endLocationId   Int?

  // Flags for location compliance
  startLocationCompliant Boolean @default(true)
  endLocationCompliant   Boolean @default(true)

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  timesheet   Timesheet? @relation(fields: [timesheetId], references: [id])
  timesheetId Int?

  // Map tracking relations
  workerLocations WorkerLocation[]

  @@index([userId])
  @@index([projectId])
  @@index([taskId])
  @@index([companyId])
  @@index([startTime])
  @@index([status])
}

// Location model to store geolocation data
model Location {
  id        Int      @id @default(autoincrement())
  latitude  Float
  longitude Float
  accuracy  Float?
  address   String?
  timestamp DateTime @default(now())

  // Relations
  timeEntryStarts TimeEntry[] @relation("StartLocation")
  timeEntryEnds   TimeEntry[] @relation("EndLocation")

  // Metadata
  createdAt DateTime @default(now())
}

// WorkLocation model to define allowed work locations
model WorkLocation {
  id          Int     @id @default(autoincrement())
  name        String
  description String?
  latitude    Float
  longitude   Float
  radius      Float // Radius in meters for the allowed work area
  address     String?
  isDefault   Boolean @default(false)
  isActive    Boolean @default(true)
  companyId   Int
  company     Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  // Map tracking relations
  workerLocations WorkerLocation[]

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
}

// Timesheet model for generating reports
model Timesheet {
  id         Int             @id @default(autoincrement())
  userId     Int
  user       User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  companyId  Int
  company    Company         @relation(fields: [companyId], references: [id], onDelete: Cascade)
  startDate  DateTime
  endDate    DateTime
  status     TimesheetStatus @default(DRAFT)
  totalHours Float
  approvedBy Int?
  approvedAt DateTime?
  notes      String?

  // Relations
  timeEntries TimeEntry[]

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([companyId])
  @@index([startDate, endDate])
  @@index([status])
}

// WorkAssignment model to assign projects/tasks to users
model WorkAssignment {
  id             Int              @id @default(autoincrement())
  userId         Int
  user           User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  projectId      Int?
  project        Project?         @relation(fields: [projectId], references: [id], onDelete: SetNull)
  taskId         Int?
  task           Task?            @relation(fields: [taskId], references: [id], onDelete: SetNull)
  companyId      Int
  company        Company          @relation(fields: [companyId], references: [id], onDelete: Cascade)
  assignedBy     Int
  startDate      DateTime
  endDate        DateTime?
  status         AssignmentStatus @default(PENDING)
  priority       Int              @default(0) // Higher number means higher priority
  estimatedHours Float?
  notes          String?

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([projectId])
  @@index([taskId])
  @@index([companyId])
  @@index([status])
}

// TaskSuggestion model for workers to suggest tasks
model TaskSuggestion {
  id          Int              @id @default(autoincrement())
  title       String
  description String
  userId      Int // Worker who suggested the task
  user        User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  projectId   Int
  project     Project          @relation(fields: [projectId], references: [id], onDelete: Cascade)
  companyId   Int
  company     Company          @relation(fields: [companyId], references: [id], onDelete: Cascade)
  status      SuggestionStatus @default(PENDING)
  reviewedBy  Int? // Project leader who reviewed the suggestion
  reviewedAt  DateTime?
  notes       String?

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([projectId])
  @@index([companyId])
  @@index([status])
}

// PayrollReport model for salary calculations
model PayrollReport {
  id          Int           @id @default(autoincrement())
  name        String
  companyId   Int
  company     Company       @relation(fields: [companyId], references: [id], onDelete: Cascade)
  startDate   DateTime
  endDate     DateTime
  status      PayrollStatus @default(DRAFT)
  totalAmount Float
  currency    String        @default("USD")
  generatedBy Int
  approvedBy  Int?
  approvedAt  DateTime?
  notes       String?

  // Relations
  payrollEntries PayrollEntry[]

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([startDate, endDate])
  @@index([status])
}

// PayrollEntry model for individual salary entries
model PayrollEntry {
  id              Int           @id @default(autoincrement())
  payrollReportId Int
  payrollReport   PayrollReport @relation(fields: [payrollReportId], references: [id], onDelete: Cascade)
  userId          Int
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  regularHours    Float
  overtimeHours   Float         @default(0)
  hourlyRate      Float
  overtimeRate    Float?
  totalAmount     Float
  deductions      Float         @default(0)
  netAmount       Float
  notes           String?

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([payrollReportId])
  @@index([userId])
}

// ClientAccess model for giving clients access to time tracking data
model ClientAccess {
  id          Int               @id @default(autoincrement())
  clientId    Int // Reference to the client user
  client      User              @relation("ClientAccess", fields: [clientId], references: [id], onDelete: Cascade)
  projectId   Int
  project     Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  companyId   Int
  company     Company           @relation(fields: [companyId], references: [id], onDelete: Cascade)
  grantedBy   Int // User who granted access
  accessLevel ClientAccessLevel @default(VIEW_ONLY)
  startDate   DateTime          @default(now())
  endDate     DateTime?
  isActive    Boolean           @default(true)

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([clientId])
  @@index([projectId])
  @@index([companyId])
}

// TimeManagementSettings model for company-specific settings
model TimeManagementSettings {
  id                        Int     @id @default(autoincrement())
  companyId                 Int     @unique
  company                   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  trackLocation             Boolean @default(true)
  enforceLocationCompliance Boolean @default(true)
  allowManualTimeEntry      Boolean @default(true)
  requireApproval           Boolean @default(true)
  roundingInterval          Int     @default(15) // Time rounding in minutes (0, 5, 10, 15, etc.)
  maxDailyHours             Int     @default(12) // Maximum allowed hours per day
  overtimeThreshold         Int     @default(8) // Hours after which overtime starts
  breakTime                 Int     @default(60) // Default break time in minutes

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Note: Relations for User, Company, Project, and Task models are defined in their respective schema files

// Content from 12-production.prisma
// Production Module Schema

// Note: Relations for User, Company, Project, Task, and QualityChecklist models are defined in their respective schema files

// Task Dependencies
model TaskDependency {
  id              Int    @id @default(autoincrement())
  parentTaskId    Int
  parentTask      Task   @relation("ParentTask", fields: [parentTaskId], references: [id], onDelete: Cascade)
  dependentTaskId Int
  dependentTask   Task   @relation("DependentTask", fields: [dependentTaskId], references: [id], onDelete: Cascade)
  type            String @default("FINISH_TO_START") // FINISH_TO_START, START_TO_START, FINISH_TO_FINISH, START_TO_FINISH

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([parentTaskId, dependentTaskId])
  @@index([parentTaskId])
  @@index([dependentTaskId])
}

// Task Assignments
model TaskAssignment {
  id          Int       @id @default(autoincrement())
  taskId      Int
  task        Task      @relation(fields: [taskId], references: [id], onDelete: Cascade)
  userId      Int
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  role        String    @default("ASSIGNEE") // ASSIGNEE, REVIEWER, OBSERVER
  assignedAt  DateTime  @default(now())
  completedAt DateTime?
  notes       String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([taskId])
  @@index([userId])
}

// Quality Checklists - this model is referenced by the ignored QualityChecklist model above
// The actual model will be defined in the combined schema

// Quality Check Items
model QualityCheckItem {
  id          Int              @id @default(autoincrement())
  checklistId Int
  checklist   QualityChecklist @relation(fields: [checklistId], references: [id], onDelete: Cascade)
  description String
  status      String           @default("PENDING") // PENDING, PASSED, FAILED, N/A
  notes       String?
  order       Int              @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([checklistId])
}

// Photo Evidence
model PhotoEvidence {
  id          Int      @id @default(autoincrement())
  projectId   Int
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  taskId      Int?
  task        Task?    @relation(fields: [taskId], references: [id], onDelete: SetNull)
  title       String
  description String?
  imageUrl    String
  takenAt     DateTime @default(now())
  takenById   Int
  takenBy     User     @relation(fields: [takenById], references: [id], onDelete: Restrict)
  location    String?
  tags        String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([projectId])
  @@index([taskId])
}

// Project Documents
model ProjectDocument {
  id           Int     @id @default(autoincrement())
  projectId    Int
  project      Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  title        String
  description  String?
  fileUrl      String
  fileType     String
  fileSize     Int
  uploadedById Int
  uploadedBy   User    @relation(fields: [uploadedById], references: [id], onDelete: Restrict)
  category     String  @default("GENERAL") // GENERAL, SPECIFICATION, CONTRACT, REPORT, OTHER
  version      String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([projectId])
}

// Project Timeline Events
model ProjectTimelineEvent {
  id          Int       @id @default(autoincrement())
  projectId   Int
  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  title       String
  description String?
  eventDate   DateTime
  endDate     DateTime?
  type        String    @default("MILESTONE") // MILESTONE, PHASE, MEETING, DEADLINE, OTHER
  status      String    @default("UPCOMING") // UPCOMING, IN_PROGRESS, COMPLETED, DELAYED
  color       String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([projectId])
}

// Equipment
model Equipment {
  id            Int       @id @default(autoincrement())
  name          String
  description   String?
  type          String
  status        String    @default("AVAILABLE") // AVAILABLE, IN_USE, MAINTENANCE, RETIRED
  serialNumber  String?
  purchaseDate  DateTime?
  purchasePrice Float?
  currentValue  Float?
  location      String?
  companyId     Int
  company       Company   @relation(fields: [companyId], references: [id], onDelete: Cascade)

  allocations     ResourceAllocation[]
  maintenanceLogs EquipmentMaintenance[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([status])
}

// Equipment Maintenance
model EquipmentMaintenance {
  id              Int       @id @default(autoincrement())
  equipmentId     Int
  equipment       Equipment @relation(fields: [equipmentId], references: [id], onDelete: Cascade)
  maintenanceDate DateTime
  description     String
  cost            Float?
  performedById   Int?
  performedBy     User?     @relation(fields: [performedById], references: [id], onDelete: SetNull)
  status          String    @default("COMPLETED") // SCHEDULED, IN_PROGRESS, COMPLETED
  notes           String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([equipmentId])
}

// Materials
model Material {
  id           Int     @id @default(autoincrement())
  name         String
  description  String?
  type         String
  unit         String  @default("PIECE") // PIECE, KG, LITER, METER, SQUARE_METER, CUBIC_METER
  unitPrice    Float?
  quantity     Float   @default(0)
  minimumStock Float?
  location     String?
  companyId    Int
  company      Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  allocations  ResourceAllocation[]
  transactions MaterialTransaction[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
}

// Material Transactions
model MaterialTransaction {
  id            Int      @id @default(autoincrement())
  materialId    Int
  material      Material @relation(fields: [materialId], references: [id], onDelete: Cascade)
  type          String // PURCHASE, USAGE, ADJUSTMENT, RETURN
  quantity      Float
  unitPrice     Float?
  totalPrice    Float?
  date          DateTime @default(now())
  projectId     Int?
  project       Project? @relation(fields: [projectId], references: [id], onDelete: SetNull)
  performedById Int
  performedBy   User     @relation(fields: [performedById], references: [id], onDelete: Restrict)
  notes         String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([materialId])
  @@index([projectId])
}

// Resource Allocation
model ResourceAllocation {
  id          Int        @id @default(autoincrement())
  projectId   Int
  project     Project    @relation(fields: [projectId], references: [id], onDelete: Cascade)
  taskId      Int?
  task        Task?      @relation(fields: [taskId], references: [id], onDelete: SetNull)
  equipmentId Int?
  equipment   Equipment? @relation(fields: [equipmentId], references: [id], onDelete: SetNull)
  materialId  Int?
  material    Material?  @relation(fields: [materialId], references: [id], onDelete: SetNull)
  userId      Int?
  user        User?      @relation(fields: [userId], references: [id], onDelete: SetNull)
  startDate   DateTime
  endDate     DateTime?
  quantity    Float?
  status      String     @default("SCHEDULED") // SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED
  notes       String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([projectId])
  @@index([taskId])
  @@index([equipmentId])
  @@index([materialId])
  @@index([userId])
}

// Production Reports
model ProductionReport {
  id          Int      @id @default(autoincrement())
  title       String
  description String?
  projectId   Int
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  reportDate  DateTime @default(now())
  reportType  String   @default("DAILY") // DAILY, WEEKLY, MONTHLY, CUSTOM
  createdById Int
  createdBy   User     @relation(fields: [createdById], references: [id], onDelete: Restrict)
  status      String   @default("DRAFT") // DRAFT, SUBMITTED, APPROVED, REJECTED

  metrics ProductionMetric[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([projectId])
  @@index([reportType])
}

// Production Metrics
model ProductionMetric {
  id       Int              @id @default(autoincrement())
  reportId Int
  report   ProductionReport @relation(fields: [reportId], references: [id], onDelete: Cascade)
  name     String
  value    Float
  unit     String?
  target   Float?
  category String           @default("EFFICIENCY") // EFFICIENCY, QUALITY, COST, TIME, OTHER
  notes    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([reportId])
  @@index([category])
}

// Content from 13-waitlist.prisma
// Waitlist Module Schema

// Waitlist email model
model WaitlistEmail {
  id                Int       @id @default(autoincrement())
  email             String    @unique
  confirmationToken String    @unique
  tokenExpiresAt    DateTime
  isConfirmed       Boolean   @default(false)
  confirmedAt       DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}

// Enum for waitlist status
enum WaitlistStatus {
  PENDING
  CONFIRMED
  EXPIRED
}

// Content from 15-calendar.prisma
// Calendar Module Schema
// Extends the business management system with calendar functionality

// Calendar Event model for calendar-specific events
model CalendarEvent {
  id          Int           @id @default(autoincrement())
  title       String
  description String?
  startDate   DateTime
  endDate     DateTime
  duration    Int // Duration in minutes
  category    EventCategory @default(GENERAL)
  status      EventStatus   @default(SCHEDULED)

  // Relations
  companyId   Int
  company     Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdById Int
  createdBy   User    @relation(fields: [createdById], references: [id])

  // Event participants
  participants EventParticipant[]

  // Event reminders
  reminders EventReminder[]

  // Event features/settings
  isRecurring    Boolean @default(false)
  recurrenceRule String? // RRULE format for recurring events
  isAllDay       Boolean @default(false)
  location       String?
  meetingUrl     String? // For virtual meetings

  // Integration with other modules
  taskId     Int? // Link to Task
  task       Task?     @relation(fields: [taskId], references: [id], onDelete: SetNull)
  activityId Int? // Link to Activity (sales)
  activity   Activity? @relation(fields: [activityId], references: [id], onDelete: SetNull)
  projectId  Int? // Link to Project
  project    Project?  @relation(fields: [projectId], references: [id], onDelete: SetNull)

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([startDate, endDate])
  @@index([createdById])
  @@index([category])
  @@index([status])
}

// Event participants - many-to-many relationship
model EventParticipant {
  id      Int           @id @default(autoincrement())
  eventId Int
  event   CalendarEvent @relation(fields: [eventId], references: [id], onDelete: Cascade)
  userId  Int
  user    User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Participant status
  status   ParticipantStatus @default(PENDING)
  response String? // Optional response message

  // Metadata
  invitedAt   DateTime  @default(now())
  respondedAt DateTime?

  @@unique([eventId, userId])
  @@index([eventId])
  @@index([userId])
  @@index([status])
}

// Calendar Event Categories
enum EventCategory {
  GENERAL // General events
  MEETING // Meetings
  TASK // Task-related events
  PROJECT // Project milestones/events
  SALES // Sales activities
  HR // HR events (training, reviews, etc.)
  PERSONAL // Personal events
  COMPANY // Company-wide events
  CLIENT // Client-related events
  DEADLINE // Deadlines
}

// Event Status
enum EventStatus {
  SCHEDULED // Event is scheduled
  IN_PROGRESS // Event is currently happening
  COMPLETED // Event has been completed
  CANCELLED // Event has been cancelled
  POSTPONED // Event has been postponed
}

// Participant Status
enum ParticipantStatus {
  PENDING // Invitation sent, no response
  ACCEPTED // Participant accepted
  DECLINED // Participant declined
  TENTATIVE // Participant marked as tentative
  NO_RESPONSE // No response received
}

// Calendar Settings per user/company
model CalendarSettings {
  id        Int      @id @default(autoincrement())
  userId    Int? // User-specific settings
  user      User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  companyId Int? // Company-wide settings
  company   Company? @relation(fields: [companyId], references: [id], onDelete: Cascade)

  // Calendar preferences
  defaultView  String @default("week") // week, month, day
  workingHours Json // { start: "09:00", end: "17:00" }
  workingDays  Json // [1,2,3,4,5] (Monday to Friday)
  timezone     String @default("UTC")

  // Notification settings
  emailNotifications Boolean @default(true)
  pushNotifications  Boolean @default(true)
  reminderMinutes    Int     @default(15) // Default reminder time

  // Display settings
  showWeekends   Boolean @default(true)
  firstDayOfWeek Int     @default(1) // 0=Sunday, 1=Monday

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId])
  @@unique([companyId])
  @@index([userId])
  @@index([companyId])
}

// Event Reminders
model EventReminder {
  id      Int           @id @default(autoincrement())
  eventId Int
  event   CalendarEvent @relation(fields: [eventId], references: [id], onDelete: Cascade)
  userId  Int
  user    User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Reminder settings
  reminderType  ReminderType
  minutesBefore Int // Minutes before event to send reminder

  // Status
  isSent Boolean   @default(false)
  sentAt DateTime?

  createdAt DateTime @default(now())

  @@index([eventId])
  @@index([userId])
  @@index([isSent])
}

// Reminder Types
enum ReminderType {
  EMAIL // Email reminder
  PUSH // Push notification
  SMS // SMS reminder
  POPUP // In-app popup
}

// Content from 15-map-tracking.prisma
// server\prisma\schema\15-map-tracking.prisma

// GPS Device for asset tracking
model GpsDevice {
  id           Int       @id @default(autoincrement())
  deviceId     String    @unique // Unique identifier from GPS device
  name         String // Human readable name
  description  String?
  isActive     Boolean   @default(true)
  batteryLevel Float? // Battery percentage (0-100)
  lastSeen     DateTime?

  // Device configuration
  reportingInterval Int    @default(300) // Seconds between reports
  geofenceRadius    Float? // Meters for geofence alerts

  // Relations
  assetId Int?
  asset   TrackableAsset? @relation(fields: [assetId], references: [id])

  // Location history
  locationHistory GpsLocation[]

  // Company relation
  companyId Int
  company   Company @relation(fields: [companyId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("gps_devices")
}

// GPS Location data points
model GpsLocation {
  id        Int      @id @default(autoincrement())
  latitude  Float
  longitude Float
  altitude  Float?
  accuracy  Float? // GPS accuracy in meters
  speed     Float? // Speed in km/h
  heading   Float? // Direction in degrees (0-360)
  timestamp DateTime

  // Device relation
  deviceId Int
  device   GpsDevice @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  // Additional metadata
  batteryLevel   Float?
  signalStrength Float?

  createdAt DateTime @default(now())

  @@index([deviceId, timestamp])
  @@index([latitude, longitude])
  @@map("gps_locations")
}

// Trackable Asset (equipment, vehicles, etc. with GPS tracking)
model TrackableAsset {
  id          Int                  @id @default(autoincrement())
  name        String
  description String?
  type        TrackableAssetType
  status      TrackableAssetStatus @default(ACTIVE)

  // Asset details
  serialNumber  String?
  model         String?
  manufacturer  String?
  purchaseDate  DateTime?
  purchasePrice Float?
  currentValue  Float?

  // Location tracking
  gpsDevices      GpsDevice[]
  currentLocation TrackableAssetLocation?

  // Company relation
  companyId Int
  company   Company @relation(fields: [companyId], references: [id])

  // Project assignments
  projectAssignments TrackableAssetAssignment[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("trackable_assets")
}

// Current location of trackable assets (cached for performance)
model TrackableAssetLocation {
  id      Int            @id @default(autoincrement())
  assetId Int            @unique
  asset   TrackableAsset @relation(fields: [assetId], references: [id], onDelete: Cascade)

  latitude   Float
  longitude  Float
  address    String?
  lastUpdate DateTime

  // Geofence status
  isInGeofence Boolean @default(true)
  geofenceName String?

  @@map("trackable_asset_locations")
}

// Trackable asset assignments to projects
model TrackableAssetAssignment {
  id        Int            @id @default(autoincrement())
  assetId   Int
  asset     TrackableAsset @relation(fields: [assetId], references: [id])
  projectId Int
  project   Project        @relation(fields: [projectId], references: [id])

  assignedAt     DateTime @default(now())
  assignedBy     Int
  assignedByUser User     @relation("AssetAssignments", fields: [assignedBy], references: [id])

  unassignedAt     DateTime?
  unassignedBy     Int?
  unassignedByUser User?     @relation("AssetUnassignments", fields: [unassignedBy], references: [id])

  notes String?

  @@unique([assetId, projectId, assignedAt])
  @@map("trackable_asset_assignments")
}

// Worker location tracking (for time management)
model WorkerLocation {
  id       Int  @id @default(autoincrement())
  workerId Int
  worker   User @relation("WorkerLocations", fields: [workerId], references: [id])

  latitude  Float
  longitude Float
  accuracy  Float?
  timestamp DateTime

  // Work context
  projectId Int?
  project   Project? @relation("WorkerProjectLocations", fields: [projectId], references: [id])
  taskId    Int?
  task      Task?    @relation("WorkerTaskLocations", fields: [taskId], references: [id])

  // Time entry relation
  timeEntryId Int?
  timeEntry   TimeEntry? @relation(fields: [timeEntryId], references: [id])

  // Geofence compliance
  isCompliant    Boolean       @default(true)
  workLocationId Int?
  workLocation   WorkLocation? @relation(fields: [workLocationId], references: [id])

  createdAt DateTime @default(now())

  @@index([workerId, timestamp])
  @@index([projectId, timestamp])
  @@map("worker_locations")
}

// Map layer configurations for different user roles
model MapLayer {
  id          Int          @id @default(autoincrement())
  name        String
  description String?
  type        MapLayerType
  isDefault   Boolean      @default(false)
  isActive    Boolean      @default(true)

  // Role-based access
  allowedRoles UserRole[] @relation("MapLayerRoles")

  // Layer configuration
  config Json? // Layer-specific configuration

  // Company relation
  companyId Int
  company   Company @relation(fields: [companyId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("map_layers")
}

// Enums
enum TrackableAssetType {
  VEHICLE
  EQUIPMENT
  TOOL
  MACHINERY
  DEVICE
  OTHER
}

enum TrackableAssetStatus {
  ACTIVE
  INACTIVE
  MAINTENANCE
  REPAIR
  LOST
  STOLEN
  DISPOSED
}

enum MapLayerType {
  PROJECTS
  WORKERS
  ASSETS
  WORK_LOCATIONS
  GEOFENCES
  CUSTOM
}

// Content from company-join-request.prisma
// Company Join Request Model
model CompanyJoinRequest {
  id              Int           @id @default(autoincrement())
  user            User          @relation(fields: [userId], references: [id])
  userId          Int
  companyName     String // The name of the company the user wants to join
  status          RequestStatus @default(PENDING)
  role            Role // The role the user wants to have in the company
  message         String? // Optional message from the user
  responseMessage String? // Optional response message from the company owner
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // If the request is approved, it will be linked to a company
  company   Company? @relation(fields: [companyId], references: [id])
  companyId Int?
  isRead    Boolean  @default(false) // Whether the request has been read by the company owner
}

// Company Invitation Model
model CompanyInvitation {
  id              Int           @id @default(autoincrement())
  company         Company       @relation(fields: [companyId], references: [id])
  companyId       Int
  email           String // Email of the invited user
  role            Role // The role the user is invited to have
  invitationToken String        @unique
  status          RequestStatus @default(PENDING)
  message         String? // Optional message from the company owner
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  expiresAt       DateTime // When the invitation expires
}

// Content from database-connection.prisma
// Database Connection model
model DatabaseConnection {
  id               Int      @id @default(autoincrement())
  name             String
  connectionString String
  type             String
  host             String
  port             Int
  database         String
  username         String
  password         String
  ssl              Boolean  @default(false)
  isActive         Boolean  @default(false)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@map("database_connection")
}

// Content from database-setting.prisma
// Database Setting model
model DatabaseSetting {
  key   String @id
  value String

  @@map("database_setting")
}

// Content from recruitment-forms.prisma
// Dynamic Form Builder Models

model RecruitmentForm {
  id              Int        @id @default(autoincrement())
  name            String
  description     String?
  slug            String     @unique // For public URL
  status          FormStatus @default(draft)
  createdBy       Int // User ID who created the form
  updatedBy       Int? // User ID who last updated the form
  isPublic        Boolean    @default(false)
  allowAnonymous  Boolean    @default(false)
  notifyEmails    String[] // Emails to notify when form is submitted
  redirectUrl     String? // URL to redirect after submission
  thankYouMessage String? // Message to show after submission

  // Form Structure
  steps  FormStep[]
  fields FormField[]

  // Form Submissions
  submissions FormSubmission[]

  // Metadata
  viewCount       Int    @default(0)
  submissionCount Int    @default(0)
  conversionRate  Float? // Calculated: submissions / views

  // Tracking
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  publishedAt DateTime?
  expiresAt   DateTime?
}

model FormStep {
  id     Int             @id @default(autoincrement())
  formId Int
  form   RecruitmentForm @relation(fields: [formId], references: [id], onDelete: Cascade)

  title       String
  description String?
  order       Int
  isEnabled   Boolean @default(true)

  // Fields in this step
  fields FormField[]

  // Translations
  translations Json? // { "et": { "title": "...", "description": "..." }, "en": { ... } }

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model FormField {
  id     Int             @id @default(autoincrement())
  formId Int
  form   RecruitmentForm @relation(fields: [formId], references: [id], onDelete: Cascade)
  stepId Int?
  step   FormStep?       @relation(fields: [stepId], references: [id], onDelete: SetNull)

  // Field Properties
  type         FieldType
  name         String // Technical name (no spaces, lowercase)
  label        String
  placeholder  String?
  helpText     String?
  defaultValue String?
  order        Int
  isRequired   Boolean   @default(false)
  isEnabled    Boolean   @default(true)
  isHidden     Boolean   @default(false)

  // Validation
  validations Json? // { "minLength": 2, "maxLength": 100, "pattern": "..." }

  // Field Options (for select, radio, checkbox)
  options FieldOption[]

  // Conditional Logic
  conditionalLogic Json? // { "show": true, "when": "fieldName", "equals": "value" }

  // Translations
  translations Json? // { "et": { "label": "...", "placeholder": "...", "helpText": "..." }, "en": { ... } }

  // Field Responses
  responses FieldResponse[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model FieldOption {
  id      Int       @id @default(autoincrement())
  fieldId Int
  field   FormField @relation(fields: [fieldId], references: [id], onDelete: Cascade)

  label     String
  value     String
  order     Int
  isDefault Boolean @default(false)

  // Translations
  translations Json? // { "et": { "label": "..." }, "en": { ... } }

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model FormSubmission {
  id     Int             @id @default(autoincrement())
  formId Int
  form   RecruitmentForm @relation(fields: [formId], references: [id], onDelete: Cascade)

  // Submission Data
  data Json // Complete submission data

  // Submitter Information
  submittedBy    Int? // User ID if authenticated
  submitterEmail String?
  submitterName  String?
  submitterIp    String?

  // Status
  status     SubmissionStatus @default(new)
  reviewedBy Int? // User ID who reviewed the submission
  reviewedAt DateTime?
  notes      String?

  // Field Responses
  responses FieldResponse[]

  // Worker Profile (if created from this submission)
  workerId Int?

  // Tracking
  referralCode String?
  source       String? // Where the submission came from

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model FieldResponse {
  id           Int            @id @default(autoincrement())
  submissionId Int
  submission   FormSubmission @relation(fields: [submissionId], references: [id], onDelete: Cascade)
  fieldId      Int
  field        FormField      @relation(fields: [fieldId], references: [id], onDelete: Cascade)

  value   String // Text value of the response
  fileUrl String? // URL to uploaded file (if field type is file)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Content from recruitment.prisma
// Workforce Application
model WorkforceApplication {
  id           Int                        @id @default(autoincrement())
  createdAt    DateTime                   @default(now())
  updatedAt    DateTime                   @updatedAt
  status       WorkforceApplicationStatus @default(new)
  referralCode String?                    @unique
  referredBy   String?

  // Personal Information
  fullName         String
  email            String
  phone            String
  dateOfBirth      DateTime?
  gender           String?
  nationality      String?
  address          String?
  preferredContact String?

  // Professional Experience
  currentJobTitle   String?
  currentEmployer   String?
  yearsOfExperience Int?
  previousJobs      WorkforcePreviousJob[]

  // Specialties and Skills
  category             String?
  role                 String?
  proficiencyLevel     String?
  secondarySpecialties WorkforceSecondarySpecialty[]
  additionalSkills     String?

  // Qualifications
  educationLevel String?
  fieldOfStudy   String?
  certifications WorkforceCertification[]
  licenses       WorkforceLicense[]
  portfolioLinks String?

  // References and Contacts
  professionalReferences WorkforceProfessionalReference[]
  emergencyContacts      WorkforceEmergencyContact[]

  // Additional Information
  languages           WorkforceLanguage[]
  availability        String?
  willingToRelocate   Boolean?
  salaryExpectations  String?
  preferredLocations  String?
  specializedTools    String?
  safetyTraining      Boolean?
  strengths           String?
  areasForImprovement String?
  hobbies             String?
}

// Previous Job
model WorkforcePreviousJob {
  id            Int                  @id @default(autoincrement())
  application   WorkforceApplication @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  applicationId Int
  company       String
  role          String
  startDate     DateTime?
  endDate       DateTime?
  duties        String?
}

// Secondary Specialty
model WorkforceSecondarySpecialty {
  id            Int                  @id @default(autoincrement())
  application   WorkforceApplication @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  applicationId Int
  specialty     String
  proficiency   String?
}

// Certification
model WorkforceCertification {
  id             Int                  @id @default(autoincrement())
  application    WorkforceApplication @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  applicationId  Int
  name           String
  organization   String?
  dateObtained   DateTime?
  expirationDate DateTime?
}

// License
model WorkforceLicense {
  id             Int                  @id @default(autoincrement())
  application    WorkforceApplication @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  applicationId  Int
  name           String
  organization   String?
  dateObtained   DateTime?
  expirationDate DateTime?
}

// Professional Reference
model WorkforceProfessionalReference {
  id            Int                  @id @default(autoincrement())
  application   WorkforceApplication @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  applicationId Int
  name          String
  relationship  String?
  contact       String
  company       String?
}

// Emergency Contact
model WorkforceEmergencyContact {
  id            Int                  @id @default(autoincrement())
  application   WorkforceApplication @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  applicationId Int
  name          String
  relationship  String?
  contact       String
}

// Language
model WorkforceLanguage {
  id            Int                  @id @default(autoincrement())
  application   WorkforceApplication @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  applicationId Int
  language      String
  proficiency   String?
}

// Form Settings
model WorkforceFormSettings {
  id              Int                  @id @default(autoincrement())
  title           String               @default("Workforce Registration")
  description     String               @default("Join our workforce database and get a chance to win 100 euros!")
  referralEnabled Boolean              @default(true)
  referralText    String               @default("Share your referral code with friends to increase your chances of winning!")
  steps           WorkforceFormStep[]
  fields          WorkforceFormField[]
}

// Form Step
model WorkforceFormStep {
  id             Int                   @id @default(autoincrement())
  formSettings   WorkforceFormSettings @relation(fields: [formSettingsId], references: [id], onDelete: Cascade)
  formSettingsId Int
  title          String
  description    String?
  enabled        Boolean               @default(true)
  order          Int
  fields         WorkforceFormField[]
}

// Form Field
model WorkforceFormField {
  id             Int                   @id @default(autoincrement())
  formSettings   WorkforceFormSettings @relation(fields: [formSettingsId], references: [id], onDelete: Cascade)
  formSettingsId Int
  step           WorkforceFormStep?    @relation(fields: [stepId], references: [id])
  stepId         Int?
  label          String
  type           String
  required       Boolean               @default(false)
  placeholder    String?
  helpText       String?
  options        Json?
  enabled        Boolean               @default(true)
  order          Int
}

// Job Posting Models
model JobPosting {
  id               Int             @id @default(autoincrement())
  title            String
  description      String
  requirements     String?
  responsibilities String?
  location         String?
  employmentType   EmploymentType  @default(FULL_TIME)
  salaryMin        Float?
  salaryMax        Float?
  currency         String          @default("EUR")
  department       String?
  experienceLevel  ExperienceLevel @default(intermediate)
  status           JobStatus       @default(DRAFT)
  isRemote         Boolean         @default(false)
  benefits         String?
  skills           String[]

  // Company relation
  companyId Int
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  // Creator
  createdBy Int
  creator   User @relation(fields: [createdBy], references: [id])

  // Applications
  applications JobApplication[]

  // Dates
  publishedAt DateTime?
  expiresAt   DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// Job Application
model JobApplication {
  id          Int        @id @default(autoincrement())
  jobId       Int
  job         JobPosting @relation(fields: [jobId], references: [id], onDelete: Cascade)
  applicantId Int?
  applicant   User?      @relation(fields: [applicantId], references: [id])

  // Application data
  applicantName  String
  applicantEmail String
  applicantPhone String?
  coverLetter    String?
  resumeUrl      String?
  portfolioUrl   String?

  // Status tracking
  status     ApplicationStatus @default(PENDING)
  stage      ApplicationStage  @default(APPLIED)
  reviewedBy Int?
  reviewer   User?             @relation("ApplicationReviewer", fields: [reviewedBy], references: [id])
  reviewedAt DateTime?
  notes      String?

  // Interview scheduling
  interviews Interview[]

  // Scoring
  score Float?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Interview
model Interview {
  id            Int            @id @default(autoincrement())
  applicationId Int
  application   JobApplication @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  title       String
  description String?
  scheduledAt DateTime
  duration    Int             @default(60) // minutes
  location    String?
  meetingUrl  String?
  type        InterviewType   @default(PHONE)
  status      InterviewStatus @default(SCHEDULED)

  // Participants
  interviewerId Int
  interviewer   User @relation(fields: [interviewerId], references: [id])

  // Feedback
  feedback String?
  rating   Float?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Assessment
model Assessment {
  id           Int            @id @default(autoincrement())
  title        String
  description  String?
  type         AssessmentType @default(TECHNICAL)
  questions    Json // Array of questions
  timeLimit    Int? // minutes
  passingScore Float?
  isActive     Boolean        @default(true)

  // Company relation
  companyId Int
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  // Creator
  createdBy Int
  creator   User @relation(fields: [createdBy], references: [id])

  // Results
  results AssessmentResult[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Assessment Result
model AssessmentResult {
  id           Int        @id @default(autoincrement())
  assessmentId Int
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  applicantId  Int?
  applicant    User?      @relation(fields: [applicantId], references: [id])

  answers   Json // Array of answers
  score     Float
  passed    Boolean
  timeSpent Int? // minutes

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Email Template
model EmailTemplate {
  id       Int               @id @default(autoincrement())
  name     String
  subject  String
  body     String
  type     EmailTemplateType @default(APPLICATION_RECEIVED)
  isActive Boolean           @default(true)

  // Company relation
  companyId Int
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  // Variables available in template
  variables String[] @default([])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Recruitment Analytics
model RecruitmentAnalytics {
  id        Int     @id @default(autoincrement())
  companyId Int
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  // Metrics
  totalJobs               Int @default(0)
  activeJobs              Int @default(0)
  totalApplications       Int @default(0)
  pendingApplications     Int @default(0)
  shortlistedApplications Int @default(0)
  interviewsScheduled     Int @default(0)
  hiredCandidates         Int @default(0)

  // Time metrics
  avgTimeToHire      Float? // days
  avgTimeToInterview Float? // days

  // Source tracking
  applicationSources Json? // { "website": 45, "linkedin": 23, ... }

  // Date range
  periodStart DateTime
  periodEnd   DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([companyId, periodStart, periodEnd])
}

// Content from workforce-intermediation.prisma
// Workforce Intermediation Models

// Import enums from 00-base.prisma
// WorkerAssignmentStatus enum is defined in 00-base.prisma

// Worker Profile (extends existing Worker model)
model WorkerProfile {
  id       Int    @id @default(autoincrement())
  worker   Worker @relation(fields: [workerId], references: [id])
  workerId Int    @unique

  // Professional Details
  specialization  String // e.g., "Welder", "Plumber", "Electrician"
  experienceYears Int
  hourlyRate      Decimal  @db.Decimal(10, 2)
  dailyRate       Decimal? @db.Decimal(10, 2)
  weeklyRate      Decimal? @db.Decimal(10, 2)
  monthlyRate     Decimal? @db.Decimal(10, 2)

  // Verification & Rating
  verificationStatus VerificationStatus @default(pending)
  verifiedAt         DateTime?
  verifiedBy         Int? // Admin or company that verified
  overallRating      Float              @default(0)
  reliabilityScore   Float              @default(0)
  qualityScore       Float              @default(0)
  punctualityScore   Float              @default(0)

  // Availability
  availableFrom      DateTime?
  currentlyAvailable Boolean   @default(true)

  // Health & Safety
  lastHealthCheck DateTime?
  healthStatus    HealthStatus?
  safetyTrainings SafetyTraining[]

  // Matching & Platform Data
  searchTags   String[] // For AI matching
  profileViews Int      @default(0)
  matchScore   Float? // AI-calculated match score

  // Relations
  jobMatches  JobMatch[]
  workHistory IntermediationAssignment[]
  reviews     WorkerReview[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Company Job Request
model JobRequest {
  id        Int     @id @default(autoincrement())
  company   Company @relation(fields: [companyId], references: [id])
  companyId Int

  // Job Details
  title                  String
  specialization         String // e.g., "Welder", "Plumber"
  description            String          @db.Text
  requiredSkills         String[]
  requiredCertifications String[]
  experienceLevel        ExperienceLevel

  // Location & Timing
  location     String
  startDate    DateTime
  endDate      DateTime? // Null for ongoing
  duration     Int? // In days
  workingHours WorkingHoursType @default(fullTime)

  // Financial
  budget   Decimal? @db.Decimal(10, 2)
  rateType RateType @default(hourly)

  // Status
  status  JobRequestStatus @default(open)
  urgency UrgencyLevel     @default(normal)

  // Matching
  preferredWorkers PreferredWorker[]
  matches          JobMatch[]
  assignments      IntermediationAssignment[]

  // Metadata
  views             Int      @default(0)
  applicationsCount Int      @default(0)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

// Preferred Worker for a Job
model PreferredWorker {
  id           Int        @id @default(autoincrement())
  jobRequest   JobRequest @relation(fields: [jobRequestId], references: [id])
  jobRequestId Int
  workerId     Int // Worker ID
  priority     Int        @default(1) // 1 = highest
  note         String?

  createdAt DateTime @default(now())
}

// AI-Generated Match between Worker and Job
model JobMatch {
  id              Int           @id @default(autoincrement())
  worker          WorkerProfile @relation(fields: [workerProfileId], references: [id])
  workerProfileId Int
  jobRequest      JobRequest    @relation(fields: [jobRequestId], references: [id])
  jobRequestId    Int

  // Match Details
  matchScore  Float // 0-100 score calculated by AI
  matchReason String?     @db.Text
  status      MatchStatus @default(pending)

  // Actions
  workerInterested  Boolean? // Null = not seen yet
  companyInterested Boolean? // Null = not seen yet

  // If both interested, creates an IntermediationAssignment
  assignment IntermediationAssignment?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([workerProfileId, jobRequestId])
}

// Work Assignment (Contract) for workforce intermediation
model IntermediationAssignment {
  id              Int           @id @default(autoincrement())
  worker          WorkerProfile @relation(fields: [workerProfileId], references: [id])
  workerProfileId Int
  jobRequest      JobRequest    @relation(fields: [jobRequestId], references: [id])
  jobRequestId    Int
  match           JobMatch?     @relation(fields: [matchId], references: [id])
  matchId         Int?          @unique

  // Contract Details
  startDate   DateTime
  endDate     DateTime?
  rate        Decimal   @db.Decimal(10, 2)
  rateType    RateType
  totalAmount Decimal?  @db.Decimal(10, 2)

  // Status
  status            WorkerAssignmentStatus @default(PENDING)
  terminationReason String?

  // Trial Period
  trialPeriod   Boolean   @default(true)
  trialEndDate  DateTime?
  trialFeedback String?   @db.Text

  // Documents
  contractDocument String? // URL to contract document

  // Reviews
  workerReview  WorkerReview?
  companyReview CompanyReview?

  // Time Tracking
  timeEntries IntermediationTimeEntry[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Time Entry for Intermediation Assignment
model IntermediationTimeEntry {
  id           Int                      @id @default(autoincrement())
  assignment   IntermediationAssignment @relation(fields: [assignmentId], references: [id])
  assignmentId Int

  date        DateTime
  hoursWorked Decimal                 @db.Decimal(5, 2)
  description String?
  status      TimeEntryApprovalStatus @default(PENDING)
  approvedBy  Int? // User ID who approved
  approvedAt  DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Worker Review
model WorkerReview {
  id              Int                      @id @default(autoincrement())
  worker          WorkerProfile            @relation(fields: [workerProfileId], references: [id])
  workerProfileId Int
  assignment      IntermediationAssignment @relation(fields: [assignmentId], references: [id])
  assignmentId    Int                      @unique

  // Review Details
  overallRating     Float // 1-5 stars
  qualityRating     Float
  reliabilityRating Float
  punctualityRating Float
  comment           String? @db.Text

  // Reviewer
  reviewedBy Int // Company representative ID
  reviewedAt DateTime @default(now())

  // Visibility
  isPublic Boolean @default(true)
}

// Company Review
model CompanyReview {
  id           Int                      @id @default(autoincrement())
  company      Company                  @relation(fields: [companyId], references: [id])
  companyId    Int
  assignment   IntermediationAssignment @relation(fields: [assignmentId], references: [id])
  assignmentId Int                      @unique

  // Review Details
  overallRating            Float // 1-5 stars
  workEnvironmentRating    Float
  managementRating         Float
  paymentPunctualityRating Float
  comment                  String? @db.Text

  // Reviewer (Worker)
  reviewedBy Int // Worker ID
  reviewedAt DateTime @default(now())

  // Visibility
  isPublic Boolean @default(true)
}

// Safety Training
model SafetyTraining {
  id              Int           @id @default(autoincrement())
  worker          WorkerProfile @relation(fields: [workerProfileId], references: [id])
  workerProfileId Int

  trainingType   String
  provider       String
  completionDate DateTime
  expiryDate     DateTime?
  certificateUrl String?
  verified       Boolean   @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Rental Company
model RentalCompany {
  id            Int     @id @default(autoincrement())
  name          String
  contactPerson String
  email         String
  phone         String
  address       String?
  website       String?

  // Partnership Details
  partnershipLevel  PartnershipLevel @default(standard)
  commissionRate    Float // Percentage
  contractStartDate DateTime
  contractEndDate   DateTime?

  // Workers provided by this rental company
  workers Worker[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
