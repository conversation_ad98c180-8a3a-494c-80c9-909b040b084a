<!--  client/.app/app/components/MapMarker.vue -->

<script setup lang="ts">
interface Props {
  id?: string | number;
  name?: string;
  description?: string;
  type?: string;
  status?: string;
  company?: string;
  project?: string;
  address?: string;
  timestamp?: string;
  accuracy?: number;
  assetType?: string;
  serialNumber?: string;
  model?: string;
  manufacturer?: string;
  budget?: number;
  startDate?: string;
  endDate?: string;
  radius?: number;
  isDefault?: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  close: [];
}>();

// Get icon based on type
function getTypeIcon(type?: string) {
  const icons = {
    project: "solar:buildings-2-linear",
    worker: "solar:users-group-rounded-linear",
    asset: "solar:box-linear",
    work_location: "solar:map-point-linear",
  };
  return icons[type as keyof typeof icons] || "solar:question-circle-linear";
}

// Get color based on type
function getTypeColor(type?: string) {
  const colors = {
    project: "text-blue-500",
    worker: "text-green-500",
    asset: "text-amber-500",
    work_location: "text-purple-500",
  };
  return colors[type as keyof typeof colors] || "text-gray-500";
}

// Get background color based on type
function getTypeBg(type?: string) {
  const backgrounds = {
    project: "bg-blue-100 dark:bg-blue-900/20",
    worker: "bg-green-100 dark:bg-green-900/20",
    asset: "bg-amber-100 dark:bg-amber-900/20",
    work_location: "bg-purple-100 dark:bg-purple-900/20",
  };
  return (
    backgrounds[type as keyof typeof backgrounds] ||
    "bg-gray-100 dark:bg-gray-900/20"
  );
}

// Format date
function formatDate(dateString?: string) {
  if (!dateString) return "";
  return new Date(dateString).toLocaleDateString();
}

// Format time
function formatTime(dateString?: string) {
  if (!dateString) return "";
  return new Date(dateString).toLocaleTimeString();
}

// Format currency
function formatCurrency(amount?: number) {
  if (!amount) return "";
  return new Intl.NumberFormat("et-EE", {
    style: "currency",
    currency: "EUR",
  }).format(amount);
}
</script>

<template>
  <BaseCard class="w-80 max-w-sm" rounded="lg">
    <!-- Header -->
    <div class="flex items-start gap-3 mb-4">
      <div
        class="flex items-center justify-center w-10 h-10 rounded-lg shrink-0"
        :class="getTypeBg(type)"
      >
        <Icon
          :name="getTypeIcon(type)"
          class="size-5"
          :class="getTypeColor(type)"
        />
      </div>
      <div class="flex-1 min-w-0">
        <h3
          class="text-muted-800 dark:text-muted-100 font-semibold text-sm leading-tight"
        >
          {{ name }}
        </h3>
        <p class="text-muted-400 text-xs mt-0.5">
          {{ type?.replace("_", " ") }}
          <template v-if="company"> • {{ company }} </template>
        </p>
      </div>
      <BaseButton
        size="sm"
        variant="muted"
        rounded="full"
        @click="emit('close')"
      >
        <Icon name="lucide:x" class="size-3" />
      </BaseButton>
    </div>

    <!-- Status indicator -->
    <div v-if="status" class="flex items-center gap-2 mb-3">
      <div
        class="w-2 h-2 rounded-full"
        :class="[
          status === 'compliant'
            ? 'bg-green-500'
            : status === 'non-compliant'
            ? 'bg-red-500'
            : status === 'active'
            ? 'bg-green-500'
            : status === 'ACTIVE'
            ? 'bg-green-500'
            : status === 'INACTIVE'
            ? 'bg-gray-400'
            : status === 'MAINTENANCE'
            ? 'bg-yellow-500'
            : 'bg-gray-400',
        ]"
      />
      <BaseText size="xs" class="text-muted-500 capitalize">
        {{ status.toLowerCase().replace("_", " ") }}
      </BaseText>
    </div>

    <!-- Description -->
    <div v-if="description" class="mb-4">
      <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
        {{ description }}
      </BaseText>
    </div>

    <!-- Project-specific info -->
    <template v-if="type === 'project'">
      <div class="space-y-2 mb-4">
        <div v-if="startDate || endDate" class="flex items-center gap-2">
          <Icon name="lucide:calendar" class="size-3 text-muted-400" />
          <BaseText size="xs" class="text-muted-500">
            <template v-if="startDate && endDate">
              {{ formatDate(startDate) }} - {{ formatDate(endDate) }}
            </template>
            <template v-else-if="startDate">
              Started: {{ formatDate(startDate) }}
            </template>
            <template v-else-if="endDate">
              Due: {{ formatDate(endDate) }}
            </template>
          </BaseText>
        </div>
        <div v-if="budget" class="flex items-center gap-2">
          <Icon name="lucide:euro" class="size-3 text-muted-400" />
          <BaseText size="xs" class="text-muted-500">
            Budget: {{ formatCurrency(budget) }}
          </BaseText>
        </div>
      </div>
    </template>

    <!-- Worker-specific info -->
    <template v-if="type === 'worker'">
      <div class="space-y-2 mb-4">
        <div v-if="project" class="flex items-center gap-2">
          <Icon name="lucide:briefcase" class="size-3 text-muted-400" />
          <BaseText size="xs" class="text-muted-500">
            Project: {{ project }}
          </BaseText>
        </div>
        <div v-if="timestamp" class="flex items-center gap-2">
          <Icon name="lucide:clock" class="size-3 text-muted-400" />
          <BaseText size="xs" class="text-muted-500">
            Last seen: {{ formatTime(timestamp) }}
          </BaseText>
        </div>
        <div v-if="accuracy" class="flex items-center gap-2">
          <Icon name="lucide:target" class="size-3 text-muted-400" />
          <BaseText size="xs" class="text-muted-500">
            Accuracy: ±{{ Math.round(accuracy) }}m
          </BaseText>
        </div>
      </div>
    </template>

    <!-- Asset-specific info -->
    <template v-if="type === 'asset'">
      <div class="space-y-2 mb-4">
        <div v-if="assetType" class="flex items-center gap-2">
          <Icon name="lucide:tag" class="size-3 text-muted-400" />
          <BaseText size="xs" class="text-muted-500">
            Type: {{ assetType }}
          </BaseText>
        </div>
        <div v-if="serialNumber" class="flex items-center gap-2">
          <Icon name="lucide:hash" class="size-3 text-muted-400" />
          <BaseText size="xs" class="text-muted-500">
            S/N: {{ serialNumber }}
          </BaseText>
        </div>
        <div v-if="model || manufacturer" class="flex items-center gap-2">
          <Icon name="lucide:info" class="size-3 text-muted-400" />
          <BaseText size="xs" class="text-muted-500">
            <template v-if="manufacturer && model">
              {{ manufacturer }} {{ model }}
            </template>
            <template v-else-if="manufacturer">
              {{ manufacturer }}
            </template>
            <template v-else>
              {{ model }}
            </template>
          </BaseText>
        </div>
        <div v-if="project" class="flex items-center gap-2">
          <Icon name="lucide:briefcase" class="size-3 text-muted-400" />
          <BaseText size="xs" class="text-muted-500">
            Assigned to: {{ project }}
          </BaseText>
        </div>
      </div>
    </template>

    <!-- Work location-specific info -->
    <template v-if="type === 'work_location'">
      <div class="space-y-2 mb-4">
        <div v-if="address" class="flex items-center gap-2">
          <Icon name="lucide:map-pin" class="size-3 text-muted-400" />
          <BaseText size="xs" class="text-muted-500">
            {{ address }}
          </BaseText>
        </div>
        <div v-if="radius" class="flex items-center gap-2">
          <Icon name="lucide:circle" class="size-3 text-muted-400" />
          <BaseText size="xs" class="text-muted-500">
            Radius: {{ radius }}m
          </BaseText>
        </div>
        <div v-if="isDefault" class="flex items-center gap-2">
          <Icon name="lucide:star" class="size-3 text-amber-500" />
          <BaseText size="xs" class="text-muted-500">
            Default location
          </BaseText>
        </div>
      </div>
    </template>

    <!-- Actions -->
    <div
      class="flex items-center gap-2 pt-3 border-t border-muted-200 dark:border-muted-700"
    >
      <BaseButton size="sm" variant="primary" class="flex-1">
        <Icon name="lucide:eye" class="size-3 mr-1" />
        View Details
      </BaseButton>
      <BaseButton size="sm" variant="muted">
        <Icon name="lucide:navigation" class="size-3" />
      </BaseButton>
      <BaseButton size="sm" variant="muted">
        <Icon name="lucide:more-horizontal" class="size-3" />
      </BaseButton>
    </div>
  </BaseCard>
</template>
