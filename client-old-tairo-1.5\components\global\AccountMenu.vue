<!-- client-old-tairo-1.5/components/global/AccountMenu.vue -->
<script setup lang="ts">
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { useUserStore } from '../../stores/useUserStore'
import { useAuthStore } from '../../stores/useAuthStore'
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'

const props = defineProps<{
  horizontal?: boolean
}>()

const userStore = useUserStore()
const authStore = useAuthStore()
const router = useRouter()

// Fetch user data if not already loaded
onMounted(async () => {
  if (authStore.isLoggedIn && !userStore.isAuthenticated) {
    try {
      await userStore.fetchUser()
    } catch (error) {
      console.error('Failed to load user:', error)
    }
  }
})

function logout() {
  // Clear auth store
  authStore.clearAuth()
  // Clear user store
  userStore.logout()
  // Redirect to home page
  router.push('/')
}

function getInitials(name: string): string {
  if (!name) return ''
  const names = name.trim().split(' ')
  if (names.length === 1) {
    return names[0].substring(0, 2).toUpperCase()
  } else {
    return (names[0][0] + names[names.length - 1][0]).toUpperCase()
  }
}
</script>

<template>
  <div class="group inline-flex items-center justify-center text-right">
    <Menu v-slot="{ close }" as="div" class="relative z-20 size-10 text-start">
      <MenuButton as="template">
        <button
          type="button"
          class="group-hover:ring-primary-500 dark:ring-offset-muted-800 inline-flex size-10 items-center justify-center rounded-full ring-1 ring-transparent transition-all duration-300 group-hover:ring-offset-4"
        >
          <div class="relative inline-flex size-10 items-center justify-center rounded-full">
            <img
              v-if="userStore.user?.avatar"
              :src="userStore.user.avatar"
              class="max-w-full rounded-full object-cover shadow-sm dark:border-transparent"
              alt="User avatar"
            />
            <div
              v-else-if="userStore.fullName"
              class="flex size-full items-center justify-center rounded-full bg-primary-500"
            >
              <span class="text-xl font-bold text-white">
                {{ getInitials(userStore.fullName) }}
              </span>
            </div>
            <img
              v-else
              src="/img/avatars/cute-astronout.svg"
              class="max-w-full rounded-full object-cover shadow-sm dark:border-transparent"
              alt="Default avatar"
            />
          </div>
        </button>
      </MenuButton>

      <Transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <MenuItems
          class="border-muted-200 dark:border-muted-700 dark:bg-muted-800 absolute mt-2 w-60 origin-bottom-right rounded-md border bg-white text-left shadow-lg focus:outline-none"
          :class="props.horizontal ? 'top-10 end-0' : 'bottom-0 -end-64'"
        >
          <div class="bg-muted-50 dark:bg-muted-700/40 p-6">
            <div class="flex items-center">
              <div class="relative inline-flex size-14 items-center justify-center rounded-full">
                <img
                  v-if="userStore.user?.avatar"
                  :src="userStore.user.avatar"
                  class="max-w-full rounded-full object-cover shadow-sm dark:border-transparent"
                  alt="User avatar"
                />
                <div
                  v-else-if="userStore.fullName"
                  class="flex size-full items-center justify-center rounded-full bg-primary-500"
                >
                  <span class="text-2xl font-bold text-white">
                    {{ getInitials(userStore.fullName) }}
                  </span>
                </div>
                <img
                  v-else
                  src="/img/avatars/cute-astronout.svg"
                  class="max-w-full rounded-full object-cover shadow-sm dark:border-transparent"
                  alt="Default avatar"
                />
              </div>
              <div class="ms-3">
                <h6 class="font-heading text-muted-800 text-sm font-medium dark:text-white">
                  {{ userStore.fullName || 'Unknown User' }}
                </h6>
                <p class="text-muted-400 font-sans text-xs">
                  {{
                    userStore.userRoles && userStore.userRoles.length > 0
                      ? userStore.userRoles[0]
                      : 'UNKNOWN'
                  }}
                </p>
              </div>
            </div>
          </div>
          <div class="p-2">
            <!-- User Profile -->
            <MenuItem v-slot="{ active }" as="div">
              <NuxtLink
                to="/users/profile"
                class="group flex w-full items-center rounded-md p-3 text-sm transition-colors duration-300"
                :class="[
                  active ? 'bg-muted-100 dark:bg-muted-700 text-primary-500' : 'text-muted-500',
                ]"
                @click.passive="close"
              >
                <Icon name="ph:user-circle-duotone" class="size-5" />
                <div class="ms-3">
                  <h6
                    class="font-heading text-muted-800 text-xs font-medium leading-none dark:text-white"
                  >
                    My Profile
                  </h6>
                  <p class="text-muted-400 font-sans text-xs">View and edit your profile</p>
                </div>
              </NuxtLink>
            </MenuItem>

            <!-- Company Profile (if user has a company) -->
            <MenuItem v-if="userStore.primaryCompany" v-slot="{ active }" as="div">
              <NuxtLink
                to="/users/company"
                class="group flex w-full items-center rounded-md p-3 text-sm transition-colors duration-300"
                :class="[
                  active ? 'bg-muted-100 dark:bg-muted-700 text-primary-500' : 'text-muted-500',
                ]"
                @click.passive="close"
              >
                <Icon name="ph:buildings-duotone" class="size-5" />
                <div class="ms-3">
                  <h6
                    class="font-heading text-muted-800 text-xs font-medium leading-none dark:text-white"
                  >
                    Company Profile
                  </h6>
                  <p class="text-muted-400 font-sans text-xs">Manage your company</p>
                </div>
              </NuxtLink>
            </MenuItem>

            <!-- Settings -->
            <MenuItem v-slot="{ active }" as="div">
              <NuxtLink
                to="/users/settings"
                class="group flex w-full items-center rounded-md p-3 text-sm transition-colors duration-300"
                :class="[
                  active ? 'bg-muted-100 dark:bg-muted-700 text-primary-500' : 'text-muted-500',
                ]"
                @click.passive="close"
              >
                <Icon name="ph:gear-six-duotone" class="size-5" />
                <div class="ms-3">
                  <h6
                    class="font-heading text-muted-800 text-xs font-medium leading-none dark:text-white"
                  >
                    Settings
                  </h6>
                  <p class="text-muted-400 font-sans text-xs">Manage your preferences</p>
                </div>
              </NuxtLink>
            </MenuItem>

            <!-- Subscriptions -->
            <MenuItem v-slot="{ active }" as="div">
              <NuxtLink
                to="/core/subscription"
                class="group flex w-full items-center rounded-md p-3 text-sm transition-colors duration-300"
                :class="[
                  active ? 'bg-muted-100 dark:bg-muted-700 text-primary-500' : 'text-muted-500',
                ]"
                @click.passive="close"
              >
                <Icon name="ph:currency-circle-dollar-duotone" class="size-5" />
                <div class="ms-3">
                  <h6
                    class="font-heading text-muted-800 text-xs font-medium leading-none dark:text-white"
                  >
                    Subscriptions
                  </h6>
                  <p class="text-muted-400 font-sans text-xs">Manage your subscriptions</p>
                </div>
              </NuxtLink>
            </MenuItem>
          </div>

          <div class="border-muted-200 dark:border-muted-700 border-b"></div>

          <div class="p-2">
            <!-- Logout Button -->
            <MenuItem v-slot="{ active }" as="div">
              <button
                class="group flex w-full items-center rounded-md p-3 text-sm transition-colors duration-300"
                :class="[
                  active ? 'bg-muted-100 dark:bg-muted-700 text-danger-500' : 'text-muted-500',
                ]"
                @click="
                  () => {
                    logout()
                    close()
                  }
                "
              >
                <Icon name="ph:sign-out-duotone" class="size-5" />
                <div class="ms-3">
                  <h6
                    class="font-heading text-muted-800 text-xs font-medium leading-none dark:text-white text-left"
                  >
                    Logout
                  </h6>
                  <p class="text-muted-400 font-sans text-xs">Sign out of your account</p>
                </div>
              </button>
            </MenuItem>
          </div>
        </MenuItems>
      </Transition>
    </Menu>
  </div>
</template>
