<!-- client/layers/production/pages/production/resources/workforce.vue -->
<template>
  <BaseLayerPage
    title="Workforce Management"
    description="Manage and track production workforce"
  >
    <!-- <PERSON> Header -->
    <template #header-actions>
      <BaseButton color="primary" @click="showNewWorkerModal = true">
        <Icon name="ph:plus-duotone" class="h-4 w-4 mr-1" />
        Add Worker
      </BaseButton>
    </template>

    <!-- Filters -->
    <div class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <BaseInput
          v-model="filters.search"
          placeholder="Search workers..."
          icon="ph:magnifying-glass-duotone"
          class="w-full"
        />

        <!-- Department Filter -->
        <BaseSelect
          v-model="filters.department"
          placeholder="Filter by Department"
          class="w-full"
        >
          <option value="">All Departments</option>
          <option value="Production">Production</option>
          <option value="Engineering">Engineering</option>
          <option value="Quality Control">Quality Control</option>
          <option value="Maintenance">Maintenance</option>
          <option value="Logistics">Logistics</option>
          <option value="Administration">Administration</option>
        </BaseSelect>

        <!-- Status Filter -->
        <BaseSelect
          v-model="filters.status"
          placeholder="Filter by Status"
          class="w-full"
        >
          <option value="">All Statuses</option>
          <option value="ACTIVE">Active</option>
          <option value="ON_LEAVE">On Leave</option>
          <option value="ASSIGNED">Assigned</option>
          <option value="AVAILABLE">Available</option>
          <option value="UNAVAILABLE">Unavailable</option>
        </BaseSelect>

        <!-- Project Filter -->
        <BaseSelect
          v-model="filters.projectId"
          placeholder="Filter by Project"
          class="w-full"
        >
          <option value="">All Projects</option>
          <option
            v-for="project in projects"
            :key="project.id"
            :value="project.id"
          >
            {{ project.name }}
          </option>
        </BaseSelect>
      </div>

      <!-- Applied Filters -->
      <div v-if="hasActiveFilters" class="flex flex-wrap gap-2 mt-3">
        <BaseTag
          v-if="filters.search"
          color="info"
          flavor="pastel"
          size="sm"
          removable
          @remove="filters.search = ''"
        >
          Search: {{ filters.search }}
        </BaseTag>
        <BaseTag
          v-if="filters.department"
          color="primary"
          flavor="pastel"
          size="sm"
          removable
          @remove="filters.department = ''"
        >
          Department: {{ filters.department }}
        </BaseTag>
        <BaseTag
          v-if="filters.status"
          color="warning"
          flavor="pastel"
          size="sm"
          removable
          @remove="filters.status = ''"
        >
          Status: {{ formatStatus(filters.status) }}
        </BaseTag>
        <BaseTag
          v-if="filters.projectId"
          color="success"
          flavor="pastel"
          size="sm"
          removable
          @remove="filters.projectId = ''"
        >
          Project: {{ getProjectName(filters.projectId) }}
        </BaseTag>
        <BaseButton
          v-if="hasActiveFilters"
          color="muted"
          flavor="link"
          size="sm"
          @click="clearFilters"
        >
          Clear All
        </BaseButton>
      </div>
    </div>

    <!-- View Toggle -->
    <div class="flex justify-end mb-4">
      <div class="inline-flex rounded-lg overflow-hidden">
        <button
          class="px-4 py-2 text-sm font-medium"
          :class="
            viewMode === 'grid'
              ? 'bg-primary-500 text-white'
              : 'bg-muted-100 dark:bg-muted-800 text-muted-500 dark:text-muted-400 hover:bg-muted-200 dark:hover:bg-muted-700'
          "
          @click="viewMode = 'grid'"
        >
          <Icon name="ph:grid-four-duotone" class="h-4 w-4" />
        </button>
        <button
          class="px-4 py-2 text-sm font-medium"
          :class="
            viewMode === 'list'
              ? 'bg-primary-500 text-white'
              : 'bg-muted-100 dark:bg-muted-800 text-muted-500 dark:text-muted-400 hover:bg-muted-200 dark:hover:bg-muted-700'
          "
          @click="viewMode = 'list'"
        >
          <Icon name="ph:list-bullets-duotone" class="h-4 w-4" />
        </button>
      </div>
    </div>

    <!-- Workforce List -->
    <div v-if="loading" class="flex justify-center py-8">
      <BaseButtonIcon shape="rounded" color="primary" loading />
    </div>

    <div
      v-else-if="error"
      class="bg-danger-100 dark:bg-danger-500/20 text-danger-500 p-4 rounded-lg mb-6"
    >
      <div class="flex items-center">
        <Icon name="ph:warning-circle-duotone" class="h-5 w-5 mr-2" />
        <BaseText>{{ error }}</BaseText>
      </div>
      <BaseButton
        color="danger"
        flavor="link"
        class="mt-2"
        @click="fetchWorkforce"
      >
        Try Again
      </BaseButton>
    </div>

    <div v-else-if="workforce.length === 0" class="text-center py-12">
      <Icon
        name="ph:users-three-duotone"
        class="h-16 w-16 text-muted-300 dark:text-muted-700 mx-auto mb-4"
      />
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        class="text-muted-800 dark:text-white mb-2"
      >
        No Workers Found
      </BaseHeading>
      <BaseParagraph
        class="text-muted-500 dark:text-muted-400 max-w-md mx-auto"
      >
        There are no workers matching your filters. Try adjusting your filters
        or add new workers.
      </BaseParagraph>
      <BaseButton
        color="primary"
        class="mt-4"
        @click="showNewWorkerModal = true"
      >
        <Icon name="ph:plus-duotone" class="h-4 w-4 mr-1" />
        Add Worker
      </BaseButton>
    </div>

    <template v-else>
      <!-- Grid View -->
      <div
        v-if="viewMode === 'grid'"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        <WorkforceCard
          v-for="worker in workforce"
          :key="worker.id"
          :worker="worker"
          @view="openWorkerDetails"
        />
      </div>

      <!-- List View -->
      <BaseCard v-else>
        <div class="overflow-x-auto">
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-muted-200 dark:border-muted-700">
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Worker
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Position
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Department
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Status
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Current Project
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Utilization
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="worker in workforce"
                :key="worker.id"
                class="border-b border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800/50"
              >
                <td class="p-3">
                  <div class="flex items-center">
                    <BaseAvatar
                      :src="worker.avatar"
                      :text="getInitials(worker)"
                      size="sm"
                      class="mr-3"
                    />
                    <div>
                      <BaseText
                        weight="medium"
                        class="text-muted-900 dark:text-white"
                      >
                        {{ worker.firstName }} {{ worker.lastName }}
                      </BaseText>
                      <BaseText size="xs" class="text-muted-400">
                        {{ worker.email }}
                      </BaseText>
                    </div>
                  </div>
                </td>
                <td class="p-3">
                  <BaseText class="text-muted-500 dark:text-muted-400">
                    {{ worker.position }}
                  </BaseText>
                </td>
                <td class="p-3">
                  <BaseText class="text-muted-500 dark:text-muted-400">
                    {{ worker.department }}
                  </BaseText>
                </td>
                <td class="p-3">
                  <BaseTag
                    :color="getStatusColor(worker.status)"
                    flavor="pastel"
                    size="sm"
                  >
                    {{ formatStatus(worker.status) }}
                  </BaseTag>
                </td>
                <td class="p-3">
                  <BaseText class="text-muted-500 dark:text-muted-400">
                    {{ worker.currentProject?.name || "Not assigned" }}
                  </BaseText>
                </td>
                <td class="p-3">
                  <div class="flex items-center">
                    <div
                      class="w-24 bg-muted-200 rounded-full h-2 dark:bg-muted-700 mr-2"
                    >
                      <div
                        class="h-2 rounded-full"
                        :class="
                          getUtilizationColorClass(worker.utilizationRate || 0)
                        "
                        :style="{ width: `${worker.utilizationRate || 0}%` }"
                      ></div>
                    </div>
                    <BaseText size="xs" class="text-muted-400">
                      {{ worker.utilizationRate || 0 }}%
                    </BaseText>
                  </div>
                </td>
                <td class="p-3">
                  <BaseButton
                    color="primary"
                    flavor="link"
                    @click="openWorkerDetails(worker)"
                  >
                    View
                  </BaseButton>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </BaseCard>
    </template>

    <!-- Pagination -->
    <div v-if="workforce.length > 0" class="mt-6 flex justify-center">
      <BasePagination
        v-model="currentPage"
        :total-items="totalItems"
        :per-page="perPage"
        :max-links="5"
        @update:model-value="handlePageChange"
      />
    </div>

    <!-- Worker Detail Modal -->
    <WorkforceDetailModal
      :is-open="showWorkerDetailModal"
      :worker-id="selectedWorkerId"
      @close="closeWorkerDetailModal"
      @refresh="fetchWorkforce"
    />

    <!-- New Worker Modal -->
    <NewWorkerModal
      :is-open="showNewWorkerModal"
      @close="showNewWorkerModal = false"
      @created="handleWorkerCreated"
    />
  </BaseLayerPage>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useProductionStore } from "../../../stores/useProductionStore";
import WorkforceCard from "../../../components/resources/WorkforceCard.vue";
import WorkforceDetailModal from "../../../components/resources/WorkforceDetailModal.vue";
import NewWorkerModal from "../../../components/resources/NewWorkerModal.vue";

const productionStore = useProductionStore();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const workforce = ref<any[]>([]);
const projects = ref<any[]>([]);
const showWorkerDetailModal = ref(false);
const showNewWorkerModal = ref(false);
const selectedWorkerId = ref("");
const currentPage = ref(1);
const perPage = ref(9);
const totalItems = ref(0);
const viewMode = ref<"grid" | "list">("grid");
const filters = ref({
  search: "",
  department: "",
  status: "",
  projectId: "",
});

// Computed
const hasActiveFilters = computed(() => {
  return (
    filters.value.search ||
    filters.value.department ||
    filters.value.status ||
    filters.value.projectId
  );
});

// Methods
const fetchWorkforce = async () => {
  loading.value = true;
  error.value = null;

  try {
    // Prepare filters
    const apiFilters: Record<string, any> = {
      page: currentPage.value,
      limit: perPage.value,
    };

    if (filters.value.search) {
      apiFilters.search = filters.value.search;
    }

    if (filters.value.department) {
      apiFilters.department = filters.value.department;
    }

    if (filters.value.status) {
      apiFilters.status = filters.value.status;
    }

    if (filters.value.projectId) {
      apiFilters.projectId = filters.value.projectId;
    }

    // Fetch workforce
    const response = await productionStore.getWorkforce(apiFilters);
    workforce.value = response.data || [];
    totalItems.value = response.total || workforce.value.length;
  } catch (err) {
    console.error("Error fetching workforce:", err);
    error.value = "Failed to load workforce. Please try again.";
  } finally {
    loading.value = false;
  }
};

const fetchProjects = async () => {
  try {
    const projectsData = await productionStore.getActiveProjects();
    projects.value = projectsData;
  } catch (error) {
    console.error("Error fetching projects:", error);
  }
};

const getInitials = (worker: any) => {
  if (!worker) return "?";
  return `${worker.firstName?.charAt(0) || ""}${
    worker.lastName?.charAt(0) || ""
  }`;
};

const formatStatus = (status: string) => {
  if (!status) return "Unknown";

  const statusMap: Record<string, string> = {
    ACTIVE: "Active",
    ON_LEAVE: "On Leave",
    ASSIGNED: "Assigned",
    AVAILABLE: "Available",
    UNAVAILABLE: "Unavailable",
  };

  return statusMap[status] || status.replace(/_/g, " ");
};

const getStatusColor = (status: string) => {
  if (!status) return "muted";

  const colorMap: Record<string, string> = {
    ACTIVE: "success",
    ON_LEAVE: "warning",
    ASSIGNED: "info",
    AVAILABLE: "success",
    UNAVAILABLE: "danger",
  };

  return colorMap[status] || "muted";
};

const getUtilizationColorClass = (rate: number) => {
  if (rate >= 80) return "bg-success-500";
  if (rate >= 50) return "bg-primary-500";
  if (rate >= 30) return "bg-warning-500";
  return "bg-muted-500";
};

const getProjectName = (projectId: string) => {
  const project = projects.value.find((p) => p.id === projectId);
  return project ? project.name : "Unknown Project";
};

const clearFilters = () => {
  filters.value = {
    search: "",
    department: "",
    status: "",
    projectId: "",
  };
  currentPage.value = 1;
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchWorkforce();
};

const openWorkerDetails = (worker: any) => {
  selectedWorkerId.value = worker.id;
  showWorkerDetailModal.value = true;
};

const closeWorkerDetailModal = () => {
  showWorkerDetailModal.value = false;
  selectedWorkerId.value = "";
};

const handleWorkerCreated = () => {
  fetchWorkforce();
};

// Watch for filter changes
watch(
  filters,
  () => {
    currentPage.value = 1;
    fetchWorkforce();
  },
  { deep: true }
);

// Initial fetch
onMounted(() => {
  fetchWorkforce();
  fetchProjects();
});
</script>
