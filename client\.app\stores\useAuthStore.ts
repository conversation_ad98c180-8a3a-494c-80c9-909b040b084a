// stores/useAuthStore.ts
import { defineStore } from "pinia";

// Helper to decode JWT without extra libs
function decodeJWT(token: string) {
  try {
    const payload = token.split(".")[1];
    const decoded = JSON.parse(atob(payload));
    return decoded;
  } catch (err) {
    console.error("Failed to decode JWT:", err);
    return null;
  }
}

export const useAuthStore = defineStore("auth", {
  state: () => ({
    userId: null as number | null,
    email: null as string | null,
    roles: [] as string[], // e.g. ['CLIENT', 'ADMIN', etc.]
    token: null as string | null,
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
  },

  actions: {
    /**
     * On app start, call this to see if there's a token in localStorage or sessionStorage.
     * If found, decode it and store user data.
     */
    hydrateAuth() {
      // Prefer localStorage first, then sessionStorage
      let storedToken = localStorage.getItem("authToken");
      if (!storedToken) {
        storedToken = sessionStorage.getItem("authToken");
      }

      if (storedToken) {
        this.setAuth(storedToken);
      }
    },

    /**
     * Sets the auth token and decodes it to fill user fields.
     */
    setAuth(token: string) {
      this.token = token;

      // Decode the JWT to get roles, userId, email
      const decoded = decodeJWT(token);
      if (decoded) {
        // e.g. { id: '24', roles: ['CLIENT'], email: '<EMAIL>', iat: 123, exp: 456 }
        if (decoded.id) this.userId = parseInt(decoded.id, 10);
        if (decoded.email) this.email = decoded.email;
        if (Array.isArray(decoded.roles)) {
          this.roles = decoded.roles;
        } else if (decoded.roles) {
          // if roles is a single string
          this.roles = [decoded.roles];
        }
      }
    },

    /**
     * Clears out everything on logout
     */
    clearAuth() {
      this.userId = null;
      this.email = null;
      this.roles = [];
      this.token = null;
      localStorage.removeItem("authToken");
      sessionStorage.removeItem("authToken");
    },
  },
});
