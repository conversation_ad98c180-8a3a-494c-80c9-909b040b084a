<script setup lang="ts">
// Import statements
import { onBeforeUnmount, onMounted, ref } from "vue";
import { useUserStore } from "../../stores/useUserStore";
import LanguageSelector from "./LanguageSelector.vue";
import AccountMenu from "./AccountMenu.vue";

const emits = defineEmits<{
  toggleMobileNav: [];
}>();

const route = useRoute();
// Setup variables
const { t, locale, locales, setLocale } = useI18n();
const userStore = useUserStore();

// PWA installation state
const isPwaInstallable = ref(false);
const deferredPrompt = ref<any>(null);

// This function is now moved to the LanguageSelector component

// Function to switch language
async function switchLanguage(code: string) {
  try {
    // Update the i18n locale first for immediate UI update
    setLocale(code);

    // Then update the user preferences in the store
    await userStore.updatePreferences({
      language: code,
    });

    // User preferences updated successfully
  } catch (error) {
    // Handle error but ensure locale is still updated
    // Even if the API call fails, we still want to update the store's local state
    userStore.$patch({
      preferences: {
        ...userStore.preferences,
        language: code,
      },
    });
  }
}

/**
 * Handle the beforeinstallprompt event for PWA installation
 */
function handleBeforeInstallPrompt(e: Event) {
  // Prevent the default browser install prompt
  e.preventDefault();

  // Store the event for later use
  deferredPrompt.value = e;

  // Show the install button
  isPwaInstallable.value = true;
}

/**
 * Open the PWA installation modal
 */
function openPwaInstallModal() {
  // Find the PwaInstallModal component in the DOM
  const modalElements = document.querySelectorAll("*");
  let modalComponent = null;

  for (const element of modalElements) {
    // @ts-ignore
    if (
      element.__vueParentComponent &&
      element.__vueParentComponent.ctx &&
      // @ts-ignore
      element.__vueParentComponent.ctx.openInstallModal
    ) {
      modalComponent = element;
      break;
    }
  }

  if (modalComponent) {
    // @ts-ignore
    modalComponent.__vueParentComponent.ctx.openInstallModal();
  } else {
    // Fallback: If we can't find the modal component, trigger the browser's native prompt
    if (deferredPrompt.value) {
      deferredPrompt.value.prompt();
      deferredPrompt.value.userChoice.then((choiceResult: any) => {
        if (choiceResult.outcome === "accepted") {
          console.warn("User accepted the PWA installation");
          localStorage.setItem("pwaInstalled", "true");
        } else {
          console.warn("User dismissed the PWA installation");
        }
        deferredPrompt.value = null;
        isPwaInstallable.value = false;
      });
    }
  }
}

/**
 * Handle the appinstalled event
 */
function handleAppInstalled() {
  // Hide the install button
  isPwaInstallable.value = false;

  // Set a flag in localStorage to remember the app was installed
  localStorage.setItem("pwaInstalled", "true");

  // Log the installation
  console.warn("PWA was installed");
}

onMounted(() => {
  // Add event listeners for PWA installation
  window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
  window.addEventListener("appinstalled", handleAppInstalled);

  // Check if the app is already installed
  const isStandalone =
    window.matchMedia("(display-mode: standalone)").matches ||
    (window.navigator as any).standalone === true;
  const isInstalledFlag = localStorage.getItem("pwaInstalled") === "true";

  if (isStandalone || isInstalledFlag) {
    isPwaInstallable.value = false;
  }
});

onBeforeUnmount(() => {
  // Clean up event listeners
  window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
  window.removeEventListener("appinstalled", handleAppInstalled);
});
</script>

<template>
  <div class="relative z-50 w-full flex items-center justify-between h-14">
    <div class="flex items-center gap-x-5 justify-center">
      <button
        type="button"
        class="flex xl:hidden items-center"
        @click="emits('toggleMobileNav')"
      >
        <span class="flex flex-col gap-1.5">
          <span class="block w-4 h-0.5 bg-muted-500" />
          <span class="block w-5 h-0.5 bg-muted-500" />
        </span>
      </button>
      <BaseHeading size="xl" weight="medium">
        {{ route.meta.title }}
      </BaseHeading>
    </div>
    <div class="flex items-center justify-end gap-x-3">
      <!-- PWA Install Button - Only shown when installation is available -->
      <BaseButton
        v-if="isPwaInstallable"
        color="primary"
        variant="default"
        class="hidden md:flex items-center gap-x-2"
        @click="openPwaInstallModal"
      >
        <Icon name="solar:download-minimalistic-linear" class="size-4" />
        <span>{{ $t("pwa.install.buttons.install") }}</span>
      </BaseButton>

      <!-- Theme toggle moved from sidebar -->
      <div class="flex items-center gap-2">
        <BaseThemeToggle class="scale-90" />

        <!-- Custom Language Selector -->
        <LanguageSelector
          :current-locale="locale"
          :available-locales="locales"
          @update:locale="switchLanguage"
          class="language-selector"
        />

        <!-- User Account Menu -->
        <AccountMenu :horizontal="true" />
      </div>
    </div>
  </div>
</template>
