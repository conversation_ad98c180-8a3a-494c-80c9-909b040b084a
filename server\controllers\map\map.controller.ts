// server/controllers/map.controller.ts

import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Get map data with role-based filtering
export const getMapData = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { layers, bounds } = req.query;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Get user roles and company
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: true,
        companies: {
          include: {
            company: true,
          },
        },
      },
    });

    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    const userRoles = user.userRoles.map((ur) => ur.role);
    const companyIds = user.companies.map((uc) => uc.companyId);

    // Parse requested layers
    const requestedLayers = layers ? (layers as string).split(",") : ["all"];

    const mapData: any = {
      type: "FeatureCollection",
      features: [],
    };

    // Get projects if requested
    if (
      requestedLayers.includes("projects") ||
      requestedLayers.includes("all")
    ) {
      const projects = await getProjectsForMap(userRoles, companyIds, userId);
      mapData.features.push(...projects);
    }

    // Get workers if requested
    if (
      requestedLayers.includes("workers") ||
      requestedLayers.includes("all")
    ) {
      const workers = await getWorkersForMap(userRoles, companyIds, userId);
      mapData.features.push(...workers);
    }

    // Get assets if requested
    if (requestedLayers.includes("assets") || requestedLayers.includes("all")) {
      const assets = await getAssetsForMap(userRoles, companyIds, userId);
      mapData.features.push(...assets);
    }

    // Get work locations if requested
    if (
      requestedLayers.includes("work_locations") ||
      requestedLayers.includes("all")
    ) {
      const workLocations = await getWorkLocationsForMap(userRoles, companyIds);
      mapData.features.push(...workLocations);
    }

    res.json(mapData);
  } catch (error) {
    console.error("Error fetching map data:", error);
    res.status(500).json({ error: "Failed to fetch map data" });
  }
};

// Get projects based on user role
async function getProjectsForMap(
  userRoles: string[],
  companyIds: number[],
  userId: number
) {
  let projectFilter: any = {};

  if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
    // Admins can see all projects in their companies
    projectFilter = { companyId: { in: companyIds } };
  } else if (userRoles.includes("PROJECTLEADER")) {
    // Project leaders see their assigned projects
    projectFilter = {
      OR: [{ projectLeadId: userId }, { companyId: { in: companyIds } }],
    };
  } else if (userRoles.includes("CLIENT")) {
    // Clients see projects they're involved in
    projectFilter = { clientId: { in: companyIds } };
  } else {
    // Workers see projects they're assigned to
    projectFilter = {
      OR: [
        { assignedWorkers: { some: { id: userId } } },
        { timeEntries: { some: { userId } } },
      ],
    };
  }

  const projects = await prisma.project.findMany({
    where: projectFilter,
    include: {
      company: true,
      client: true,
    },
  });

  // For now, return projects with default coordinates (Tallinn, Estonia)
  // In a real implementation, you would get coordinates from project addresses or work locations
  return projects.map((project) => ({
    type: "Feature",
    id: `project-${project.id}`,
    properties: {
      id: project.id,
      name: project.name,
      description: project.description,
      status: project.status,
      type: "project",
      company: project.company.name,
      client: project.client.name,
      startDate: project.startDate,
      endDate: project.endDate,
      budget: project.budget,
    },
    geometry: {
      type: "Point",
      coordinates: [
        24.7536 + (Math.random() - 0.5) * 0.1, // Random offset around Tallinn
        59.437 + (Math.random() - 0.5) * 0.1,
      ],
    },
  }));
}

// Get worker locations based on user role
async function getWorkersForMap(
  userRoles: string[],
  companyIds: number[],
  userId: number
) {
  let workerFilter: any = {};

  if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
    // Admins can see all workers in their companies
    workerFilter = {
      companies: { some: { companyId: { in: companyIds } } },
    };
  } else if (userRoles.includes("PROJECTLEADER")) {
    // Project leaders see workers on their projects
    workerFilter = {
      OR: [
        { assignedProjects: { some: { projectLeadId: userId } } },
        { companies: { some: { companyId: { in: companyIds } } } },
      ],
    };
  } else if (userRoles.includes("CLIENT")) {
    // Clients see workers on their projects
    workerFilter = {
      assignedProjects: { some: { clientId: { in: companyIds } } },
    };
  } else {
    // Workers see only themselves
    workerFilter = { id: userId };
  }

  // For now, return mock worker data with random locations around Tallinn
  const workers = await prisma.user.findMany({
    where: workerFilter,
    include: {
      companies: {
        include: { company: true },
      },
    },
  });

  return workers.map((worker) => ({
    type: "Feature",
    id: `worker-${worker.id}`,
    properties: {
      id: worker.id,
      name: `${worker.firstName} ${worker.lastName}`,
      firstName: worker.firstName,
      lastName: worker.lastName,
      avatar: worker.avatar,
      type: "worker",
      status: "compliant",
      project: "Sample Project",
      task: "Sample Task",
      timestamp: new Date().toISOString(),
      accuracy: 5,
      company: worker.companies[0]?.company?.name || "Unknown Company",
    },
    geometry: {
      type: "Point",
      coordinates: [
        24.7536 + (Math.random() - 0.5) * 0.05, // Random offset around Tallinn
        59.437 + (Math.random() - 0.5) * 0.05,
      ],
    },
  }));
}

// Get trackable assets based on user role
async function getAssetsForMap(
  userRoles: string[],
  companyIds: number[],
  userId: number
) {
  let assetFilter: any = {};

  if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
    // Admins can see all assets in their companies
    assetFilter = { companyId: { in: companyIds } };
  } else if (userRoles.includes("PROJECTLEADER")) {
    // Project leaders see assets on their projects
    assetFilter = {
      OR: [
        { companyId: { in: companyIds } },
        {
          projectAssignments: { some: { project: { projectLeadId: userId } } },
        },
      ],
    };
  } else if (userRoles.includes("CLIENT")) {
    // Clients see assets on their projects
    assetFilter = {
      projectAssignments: {
        some: { project: { clientId: { in: companyIds } } },
      },
    };
  } else {
    // Workers see assets they're assigned to work with
    assetFilter = {
      projectAssignments: {
        some: {
          project: {
            OR: [
              { assignedWorkers: { some: { id: userId } } },
              { timeEntries: { some: { userId } } },
            ],
          },
        },
      },
    };
  }

  // For now, return mock asset data
  // In a real implementation, you would query the trackableAsset table
  const mockAssets = [
    {
      id: 1,
      name: "Excavator CAT 320",
      description: "Heavy machinery for construction",
      type: "MACHINERY",
      status: "ACTIVE",
      serialNumber: "CAT320-001",
      model: "320",
      manufacturer: "Caterpillar",
      company: "Sample Company",
      project: "Construction Project A",
      lastUpdate: new Date(),
      isInGeofence: true,
      coordinates: [24.7536 + 0.01, 59.437 + 0.01],
    },
    {
      id: 2,
      name: "Delivery Van",
      description: "Company delivery vehicle",
      type: "VEHICLE",
      status: "ACTIVE",
      serialNumber: "VAN-002",
      model: "Transit",
      manufacturer: "Ford",
      company: "Sample Company",
      project: "Delivery Operations",
      lastUpdate: new Date(),
      isInGeofence: true,
      coordinates: [24.7536 - 0.01, 59.437 - 0.01],
    },
  ];

  return mockAssets.map((asset) => ({
    type: "Feature",
    id: `asset-${asset.id}`,
    properties: {
      id: asset.id,
      name: asset.name,
      description: asset.description,
      type: "asset",
      assetType: asset.type,
      status: asset.status,
      serialNumber: asset.serialNumber,
      model: asset.model,
      manufacturer: asset.manufacturer,
      company: asset.company,
      project: asset.project,
      lastUpdate: asset.lastUpdate,
      isInGeofence: asset.isInGeofence,
    },
    geometry: {
      type: "Point",
      coordinates: asset.coordinates,
    },
  }));
}

// Get work locations based on user role
async function getWorkLocationsForMap(
  userRoles: string[],
  companyIds: number[]
) {
  const workLocations = await prisma.workLocation.findMany({
    where: {
      companyId: { in: companyIds },
      isActive: true,
    },
    include: {
      company: true,
    },
  });

  return workLocations.map((location) => ({
    type: "Feature",
    id: `work-location-${location.id}`,
    properties: {
      id: location.id,
      name: location.name,
      description: location.description,
      type: "work_location",
      address: location.address,
      radius: location.radius,
      isDefault: location.isDefault,
      company: location.company.name,
    },
    geometry: {
      type: "Point",
      coordinates: [location.longitude, location.latitude],
    },
  }));
}
