// server/controllers/map.controller.ts

import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Get map data with role-based filtering
export const getMapData = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { layers, bounds } = req.query;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Get user roles and company
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: true,
        companies: {
          include: {
            company: true,
          },
        },
      },
    });

    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    const userRoles = user.userRoles.map((ur) => ur.role);
    const companyIds = user.companies.map((uc) => uc.companyId);

    // Parse requested layers
    const requestedLayers = layers ? (layers as string).split(",") : ["all"];

    const mapData: any = {
      type: "FeatureCollection",
      features: [],
    };

    // Get projects if requested
    if (
      requestedLayers.includes("projects") ||
      requestedLayers.includes("all")
    ) {
      const projects = await getProjectsForMap(userRoles, companyIds, userId);
      mapData.features.push(...projects);
    }

    // Get workers if requested
    if (
      requestedLayers.includes("workers") ||
      requestedLayers.includes("all")
    ) {
      const workers = await getWorkersForMap(userRoles, companyIds, userId);
      mapData.features.push(...workers);
    }

    // Get rental workers if requested
    if (
      requestedLayers.includes("rental_workers") ||
      requestedLayers.includes("all")
    ) {
      const rentalWorkers = await getRentalWorkersForMap(
        userRoles,
        companyIds,
        userId
      );
      mapData.features.push(...rentalWorkers);
    }

    // Get assets if requested
    if (requestedLayers.includes("assets") || requestedLayers.includes("all")) {
      const assets = await getAssetsForMap(userRoles, companyIds, userId);
      mapData.features.push(...assets);
    }

    // Get work locations if requested
    if (
      requestedLayers.includes("work_locations") ||
      requestedLayers.includes("all")
    ) {
      const workLocations = await getWorkLocationsForMap(userRoles, companyIds);
      mapData.features.push(...workLocations);
    }

    res.json(mapData);
  } catch (error) {
    console.error("Error fetching map data:", error);
    res.status(500).json({ error: "Failed to fetch map data" });
  }
};

// Get projects based on user role
async function getProjectsForMap(
  userRoles: string[],
  companyIds: number[],
  userId: number
) {
  let projectFilter: any = {};

  if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
    // Admins can see all projects in their companies
    projectFilter = { companyId: { in: companyIds } };
  } else if (userRoles.includes("PROJECTLEADER")) {
    // Project leaders see their assigned projects
    projectFilter = {
      OR: [{ projectLeadId: userId }, { companyId: { in: companyIds } }],
    };
  } else if (userRoles.includes("CLIENT")) {
    // Clients see projects they're involved in
    projectFilter = { clientId: { in: companyIds } };
  } else {
    // Workers see projects they're assigned to
    projectFilter = {
      OR: [
        { assignedWorkers: { some: { id: userId } } },
        { timeEntries: { some: { userId } } },
      ],
    };
  }

  const projects = await prisma.project.findMany({
    where: projectFilter,
    include: {
      company: true,
      client: true,
    },
  });

  // Return projects with real coordinates from database
  return projects.map((project) => ({
    type: "Feature",
    id: `project-${project.id}`,
    properties: {
      id: project.id,
      name: project.name,
      description: project.description,
      status: project.status,
      type: "project",
      company: project.company.name,
      client: project.client.name,
      startDate: project.startDate,
      endDate: project.endDate,
      budget: project.budget,
      address: project.address,
    },
    geometry: {
      type: "Point",
      coordinates: [
        project.longitude || 24.7536, // Use stored coordinates or default to Tallinn
        project.latitude || 59.437,
      ],
    },
  }));
}

// Get worker locations based on user role
async function getWorkersForMap(
  userRoles: string[],
  companyIds: number[],
  userId: number
) {
  let workerFilter: any = {};

  if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
    // Admins can see all workers in their companies
    workerFilter = {
      companies: { some: { companyId: { in: companyIds } } },
    };
  } else if (userRoles.includes("PROJECTLEADER")) {
    // Project leaders see workers on their projects
    workerFilter = {
      OR: [
        { assignedProjects: { some: { projectLeadId: userId } } },
        { companies: { some: { companyId: { in: companyIds } } } },
      ],
    };
  } else if (userRoles.includes("CLIENT")) {
    // Clients see workers on their projects
    workerFilter = {
      assignedProjects: { some: { clientId: { in: companyIds } } },
    };
  } else {
    // Workers see only themselves
    workerFilter = { id: userId };
  }

  // Get workers with their latest location data
  const workers = await prisma.user.findMany({
    where: workerFilter,
    include: {
      companies: {
        include: { company: true },
      },
      workerLocations: {
        orderBy: { timestamp: "desc" },
        take: 1,
        include: {
          project: true,
          task: true,
          workLocation: true,
        },
      },
    },
  });

  return workers.map((worker) => {
    const latestLocation = worker.workerLocations[0];

    return {
      type: "Feature",
      id: `worker-${worker.id}`,
      properties: {
        id: worker.id,
        name: `${worker.firstName} ${worker.lastName}`,
        firstName: worker.firstName,
        lastName: worker.lastName,
        avatar: worker.avatar,
        type: "worker",
        status: latestLocation?.isCompliant ? "compliant" : "non-compliant",
        project: latestLocation?.project?.name || "Unassigned project",
        task: latestLocation?.task?.name || "Unassigned task",
        timestamp:
          latestLocation?.timestamp?.toISOString() || new Date().toISOString(),
        accuracy: latestLocation?.accuracy || 5,
        company: worker.companies[0]?.company?.name || "Unknown company",
      },
      geometry: {
        type: "Point",
        coordinates: [
          latestLocation?.longitude || 24.7536,
          latestLocation?.latitude || 59.437,
        ],
      },
    };
  });
}

// Get trackable assets based on user role
async function getAssetsForMap(
  userRoles: string[],
  companyIds: number[],
  userId: number
) {
  let assetFilter: any = {};

  if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
    // Admins can see all assets in their companies
    assetFilter = { companyId: { in: companyIds } };
  } else if (userRoles.includes("PROJECTLEADER")) {
    // Project leaders see assets on their projects
    assetFilter = {
      OR: [
        { companyId: { in: companyIds } },
        {
          projectAssignments: { some: { project: { projectLeadId: userId } } },
        },
      ],
    };
  } else if (userRoles.includes("CLIENT")) {
    // Clients see assets on their projects
    assetFilter = {
      projectAssignments: {
        some: { project: { clientId: { in: companyIds } } },
      },
    };
  } else {
    // Workers see assets they're assigned to work with
    assetFilter = {
      projectAssignments: {
        some: {
          project: {
            OR: [
              { assignedWorkers: { some: { id: userId } } },
              { timeEntries: { some: { userId } } },
            ],
          },
        },
      },
    };
  }

  // Get real trackable assets from database
  const assets = await prisma.trackableAsset.findMany({
    where: assetFilter,
    include: {
      company: true,
      currentLocation: true,
      projectAssignments: {
        include: {
          project: true,
        },
        orderBy: { assignedAt: "desc" },
        take: 1,
      },
    },
  });

  return assets.map((asset) => {
    const currentProject = asset.projectAssignments[0]?.project;

    return {
      type: "Feature",
      id: `asset-${asset.id}`,
      properties: {
        id: asset.id,
        name: asset.name,
        description: asset.description,
        type: "asset",
        assetType: asset.type,
        status: asset.status,
        serialNumber: asset.serialNumber,
        model: asset.model,
        manufacturer: asset.manufacturer,
        company: asset.company.name,
        project: currentProject?.name || "Unassigned project",
        lastUpdate: asset.currentLocation?.lastUpdate || asset.updatedAt,
        isInGeofence: asset.currentLocation?.isInGeofence || true,
      },
      geometry: {
        type: "Point",
        coordinates: [
          asset.currentLocation?.longitude || 24.7536,
          asset.currentLocation?.latitude || 59.437,
        ],
      },
    };
  });
}

// Get work locations based on user role
async function getWorkLocationsForMap(
  userRoles: string[],
  companyIds: number[]
) {
  const workLocations = await prisma.workLocation.findMany({
    where: {
      companyId: { in: companyIds },
      isActive: true,
    },
    include: {
      company: true,
    },
  });

  return workLocations.map((location) => ({
    type: "Feature",
    id: `work-location-${location.id}`,
    properties: {
      id: location.id,
      name: location.name,
      description: location.description,
      type: "work_location",
      address: location.address,
      radius: location.radius,
      isDefault: location.isDefault,
      company: location.company.name,
    },
    geometry: {
      type: "Point",
      coordinates: [location.longitude, location.latitude],
    },
  }));
}

// Get rental worker locations based on user role
async function getRentalWorkersForMap(
  userRoles: string[],
  companyIds: number[],
  userId: number
) {
  let rentalFilter: any = {};

  if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
    // Admins can see all rental workers in their companies
    rentalFilter = {
      OR: [
        { fromCompanyId: { in: companyIds } },
        { toCompanyId: { in: companyIds } },
      ],
      status: "ACTIVE",
    };
  } else if (userRoles.includes("PROJECTLEADER")) {
    // Project leaders see rental workers on their projects
    rentalFilter = {
      toCompanyId: { in: companyIds },
      status: "ACTIVE",
    };
  } else if (userRoles.includes("CLIENT")) {
    // Clients see rental workers on their projects
    rentalFilter = {
      toCompanyId: { in: companyIds },
      status: "ACTIVE",
    };
  } else {
    // Workers see only themselves if they are rental workers
    rentalFilter = {
      workerId: userId,
      status: "ACTIVE",
    };
  }

  // Get rental workers with their latest location data
  const rentalWorkers = await prisma.workerRental.findMany({
    where: rentalFilter,
    include: {
      worker: true,
      fromCompany: true,
      toCompany: true,
    },
  });

  // Get worker locations separately for rental workers
  const workerIds = rentalWorkers.map((rental) => rental.workerId);
  const workerLocations = await prisma.workerLocation.findMany({
    where: {
      workerId: { in: workerIds },
    },
    orderBy: { timestamp: "desc" },
    distinct: ["workerId"],
  });

  return rentalWorkers.map((rental) => {
    const worker = rental.worker;
    const latestLocation = workerLocations.find(
      (loc) => loc.workerId === worker.id
    );

    return {
      type: "Feature",
      id: `rental-worker-${worker.id}`,
      geometry: {
        type: "Point",
        coordinates: latestLocation
          ? [latestLocation.longitude, latestLocation.latitude]
          : [24.7536, 59.437], // Default to Tallinn if no location
      },
      properties: {
        type: "rental_worker",
        name: `${worker.firstName} ${worker.lastName}`,
        firstName: worker.firstName,
        lastName: worker.lastName,
        avatar: worker.avatar,
        project: "Unassigned project",
        task: "Unassigned task",
        timestamp: latestLocation?.timestamp || new Date(),
        accuracy: latestLocation?.accuracy || 0,
        company: rental.fromCompany?.name || "Unknown company",
        rentalCompany: rental.fromCompany?.name,
        clientCompany: rental.toCompany?.name,
        hourlyRate: rental.hourlyRate,
        status: rental.status,
        startDate: rental.startDate,
        endDate: rental.endDate,
      },
    };
  });
}
