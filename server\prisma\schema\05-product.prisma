model ProjectProduct {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  projectId   Int?
  project     Project?  @relation("ProjectProducts", fields: [projectId], references: [id])
}

model Project {
  id            Int       @id @default(autoincrement())
  name          String
  description   String?
  status        ProjectStatus
  startDate     DateTime
  endDate       DateTime?
  companyId     Int
  clientId      Int
  projectLeadId Int
  budget        Float
  barcode       String    @unique
  qrCode        String    @unique

  // Location fields
  latitude      Float?
  longitude     Float?
  address       String?

  // Add relationships
  company       Company   @relation("CompanyProjects", fields: [companyId], references: [id])
  client        Company   @relation("ClientProjects", fields: [clientId], references: [id])
  projectLead   Worker    @relation("ProjectLeads", fields: [projectLeadId], references: [id])
  assignedWorkers Worker[] @relation("ProjectWorkers")  // Many-to-many relationship with workers

  // Existing relationships
  tasks         Task[]
  documents     Document[]
  qcChecklists  QualityChecklist[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  Worker Worker[] @relation("LedProjects")

  products      ProjectProduct[] @relation("ProjectProducts")
  stages        Stage[]

  // Time management relations
  timeEntries       TimeEntry[]
  workAssignments   WorkAssignment[]
  taskSuggestions   TaskSuggestion[]
  clientAccesses    ClientAccess[]

  // Production module relations
  photoEvidences       PhotoEvidence[]
  projectDocuments     ProjectDocument[]
  timelineEvents       ProjectTimelineEvent[]
  resourceAllocations  ResourceAllocation[]
  productionReports    ProductionReport[]
  materialTransactions MaterialTransaction[]

  // Calendar relations
  calendarEvents       CalendarEvent[]

  // Map and location tracking relations
  workerLocations      WorkerLocation[] @relation("WorkerProjectLocations")
  assetAssignments     TrackableAssetAssignment[]
}

model Task {
  id            Int       @id @default(autoincrement())
  projectId     Int
  companyId     Int       // Add company relation
  name          String
  description   String?
  status        TaskStatus
  assignedTo    Int?
  startDate     DateTime?
  endDate       DateTime?
  priority      Priority
  qrCode        String    @unique
  barcode       String    @unique
  documents     Document[]
  qcSteps       QualityStep[]

  company       Company   @relation("CompanyTasks", fields: [companyId], references: [id])
  project       Project   @relation(fields: [projectId], references: [id])
  worker        Worker?   @relation("AssignedTasks", fields: [assignedTo], references: [id])

  // Map and location tracking relations
  workerLocations WorkerLocation[] @relation("WorkerTaskLocations")

  // Task dependencies
  dependsOn     TaskDependency[] @relation("DependentTask")
  dependentTasks TaskDependency[] @relation("ParentTask")

  // Task assignments
  assignments    TaskAssignment[]

  // Resource allocations
  resourceAllocations ResourceAllocation[]

  // Photo evidence
  photoEvidences PhotoEvidence[]

  // Time entries
  timeEntries    TimeEntry[]

  // Work assignments
  workAssignments WorkAssignment[]

  // Calendar relations
  calendarEvents CalendarEvent[]
}

model QualityChecklist {
  id          Int       @id @default(autoincrement())
  projectId   Int
  companyId   Int       // Add company relation
  name        String
  steps       QualityStep[]
  items       QualityCheckItem[]
  createdById Int
  createdBy   User     @relation("ChecklistCreator", fields: [createdById], references: [id])
  checkedById Int?
  checkedBy   User?    @relation("ChecklistReviewer", fields: [checkedById], references: [id])

  company     Company   @relation("CompanyQualityChecklists", fields: [companyId], references: [id])
  project     Project   @relation(fields: [projectId], references: [id])
}

model QualityStep {
  id          Int       @id @default(autoincrement())
  taskId      Int
  checklistId Int
  companyId   Int       // Add company relation
  name        String
  description String?
  required    Boolean   @default(true)
  completed   Boolean   @default(false)
  photos      Photo[]

  company     Company   @relation("CompanyQualitySteps", fields: [companyId], references: [id])
  task        Task      @relation(fields: [taskId], references: [id])
  checklist   QualityChecklist @relation(fields: [checklistId], references: [id])
}

model Photo {
  id          Int       @id @default(autoincrement())
  stepId      Int
  companyId   Int       // Add company relation
  url         String
  timestamp   DateTime  @default(now())

  company     Company   @relation("CompanyPhotos", fields: [companyId], references: [id])
  step        QualityStep @relation(fields: [stepId], references: [id])
}

model Document {
  id          Int       @id @default(autoincrement())
  projectId   Int?
  taskId      Int?
  companyId   Int       // Add company relation
  name        String
  url         String
  type        DocumentType
  description String?
  size        Int       @default(0)
  mimeType    String?
  uploadedBy  Int
  category    String?
  module      String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  company     Company   @relation("CompanyDocuments", fields: [companyId], references: [id])
  project     Project?  @relation(fields: [projectId], references: [id])
  task        Task?     @relation(fields: [taskId], references: [id])
  Employee Employee[] @relation("EmployeeDocuments")
}

model Stage {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  project     Project  @relation(fields: [projectId], references: [id])
  projectId   Int

  // Add other stage-related fields as needed
}