<!-- client/.app/app/layouts/default.vue -->

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { useCommunicationStore } from "../../stores/useCommunicationStore";
import { useUserStore } from "../../stores/useUserStore";
import { useSearchOpen } from "../composables/tairo-demo-state.js";
import SubsidebarChat from "../components/SubsidebarChat.vue";
import CalendarSidebarCategories from "../components/CalendarSidebarCategories.vue";

const { t, locale, setLocale } = useI18n();

const userStore = useUserStore();

// Function to handle creating new events from sidebar
function handleCreateNewEvent() {
  // Navigate to calendar page with create action
  navigateTo("/calendar?action=create");
}

// Initialize language from user preferences when component is mounted
onMounted(() => {
  userStore.initializeLanguage({
    setLocale: (locale) => setLocale(locale as "en" | "et"),
  });
});

// Create a reactive reference to track language changes
const currentLanguage = ref(locale.value);

// Force the locale to match the user preference on component mount
onMounted(() => {
  if (userStore.preferences.language) {
    // Set locale based on user preferences
    setLocale(userStore.preferences.language as "en" | "et");
  }
});

// Watch for changes in the user's language preference
watch(
  () => userStore.preferences.language,
  (newLanguage) => {
    // Update locale when language preference changes

    if (newLanguage && locale.value !== newLanguage) {
      // Update the i18n locale directly
      setLocale(newLanguage as "en" | "et");
    }
  },
  { immediate: true }
);

// Also watch for direct changes to the i18n locale
watch(
  () => locale.value,
  (newLocale) => {
    // Track locale changes
  }
);

// Initialize the communication store
const communicationStore = useCommunicationStore();

// Fetch contact form messages when the component is mounted
onMounted(async () => {
  try {
    await communicationStore.fetchContactFormMessages();
  } catch (error) {
    console.error("Error fetching contact form messages:", error);
  }
});

// Make menu reactive to language changes
const menu = computed(() => [
  {
    label: t("navigation.core"),
    icon: "solar:widget-2-linear",
    links: [
      {
        label: t("navigation.overview"),
        icon: "solar:widget-2-linear",
        children: [
          {
            label: t("navigation.dashboard"),
            to: "/core",
            icon: "solar:widget-2-linear",
            exact: true,
          },
          {
            label: t("navigation.system_health"),
            to: "/core/health",
            icon: "solar:heart-pulse-linear",
          },
          {
            label: t("navigation.activity_logs"),
            to: "/core/activity",
            icon: "solar:clipboard-check-linear",
          },
        ],
      },
      {
        label: t("navigation.business_docs"),
        icon: "solar:documents-linear",
        children: [
          {
            label: t("navigation.business_plan"),
            to: "/core/business/plan",
            icon: "solar:document-linear",
          },
          {
            label: t("navigation.mission_vision"),
            to: "/core/business/mission-vision",
            icon: "solar:flag-linear",
          },
          {
            label: t("navigation.company_policies"),
            to: "/core/business/policies",
            icon: "solar:book-bookmark-linear",
          },
          {
            label: t("navigation.legal_documents"),
            to: "/core/business/legal",
            icon: "solar:scale-linear",
          },
          {
            label: t("navigation.brand_assets"),
            to: "/core/business/brand",
            icon: "solar:paint-linear",
          },
          {
            label: t("navigation.document_library"),
            to: "/core/business/documents",
            icon: "solar:documents-linear",
          },
        ],
      },
      {
        label: t("navigation.business_rules"),
        icon: "solar:settings-linear",
        children: [
          {
            label: t("navigation.all_rules"),
            to: "/core/rules",
            icon: "solar:settings-linear",
          },
          {
            label: t("navigation.rule_categories"),
            to: "/core/rules/categories",
            icon: "solar:folder-with-files-linear",
          },
          {
            label: t("navigation.rule_templates"),
            to: "/core/rules/templates",
            icon: "solar:document-text-linear",
          },
          {
            label: t("navigation.rule_validation"),
            to: "/core/rules/validation",
            icon: "solar:check-circle-linear",
          },
        ],
      },
      {
        label: t("navigation.system_integration"),
        icon: "solar:plug-circle-linear",
        children: [
          {
            label: t("navigation.module_status"),
            to: "/core/modules",
            icon: "solar:widget-2-linear",
          },
          {
            label: t("navigation.api_management"),
            to: "/core/api",
            icon: "solar:plug-circle-linear",
          },
          {
            label: t("navigation.webhooks"),
            to: "/core/webhooks",
            icon: "solar:link-linear",
          },
          {
            label: t("navigation.event_bus"),
            to: "/core/events",
            icon: "solar:broadcast-linear",
          },
        ],
      },
      {
        label: t("navigation.ai_automation"),
        icon: "solar:chart-square-linear",
        children: [
          {
            label: t("navigation.ai_decisions"),
            to: "/core/ai-decisions",
            icon: "solar:brain-linear",
          },
          {
            label: t("navigation.assistant_settings"),
            to: "/core/ai-settings",
            icon: "solar:robot-linear",
          },
          {
            label: t("navigation.automation_rules"),
            to: "/core/automation",
            icon: "solar:magic-stick-linear",
          },
          {
            label: t("navigation.ml_models"),
            to: "/core/ml-models",
            icon: "solar:code-linear",
          },
        ],
      },
      {
        label: t("navigation.security"),
        icon: "solar:shield-check-linear",
        children: [
          {
            label: t("navigation.access_control"),
            to: "/core/security/access-control-fixed",
            icon: "solar:shield-check-linear",
          },
          {
            label: t("navigation.access_management"),
            to: "/core/security/access-management",
            icon: "solar:lock-keyhole-linear",
          },
          {
            label: t("navigation.audit_logs"),
            to: "/core/security/audit-logs",
            icon: "solar:scroll-linear",
          },
          {
            label: t("navigation.security_settings"),
            to: "/core/security/settings",
            icon: "solar:lock-keyhole-linear",
          },
        ],
      },
      {
        label: t("navigation.configuration"),
        icon: "solar:tuning-square-linear",
        children: [
          {
            label: t("navigation.global_settings"),
            to: "/core/settings",
            icon: "solar:tuning-square-linear",
          },
          {
            label: t("navigation.environment"),
            to: "/core/settings/env",
            icon: "solar:tree-linear",
          },
          {
            label: t("navigation.database_management"),
            to: "/core/settings/database",
            icon: "solar:database-linear",
          },
          {
            label: t("navigation.localization"),
            to: "/core/settings/locale",
            icon: "solar:translate-linear",
          },
          {
            label: t("navigation.backup_restore"),
            to: "/core/settings/backup",
            icon: "solar:cloud-linear",
          },
          {
            label: t("navigation.subscription"),
            to: "/core/subscription",
            icon: "solar:dollar-minimalistic-linear",
          },
          {
            label: t("navigation.subscription_plans"),
            to: "/core/subscription/plans",
            icon: "solar:layers-linear",
            permissions: ["SUPERADMIN"],
          },
        ],
      },
      {
        label: t("navigation.monitoring"),
        icon: "solar:chart-square-linear",
        children: [
          {
            label: t("navigation.performance"),
            to: "/core/monitoring/performance",
            icon: "solar:chart-square-linear",
          },
          {
            label: t("navigation.error_logs"),
            to: "/core/monitoring/errors",
            icon: "solar:bug-linear",
          },
          {
            label: t("navigation.resource_usage"),
            to: "/core/monitoring/resources",
            icon: "solar:cpu-linear",
          },
          {
            label: t("navigation.alerts"),
            to: "/core/monitoring/alerts",
            icon: "solar:bell-bing-linear",
          },
        ],
      },
    ],
  },
  {
    label: t("navigation.workforce_hub"),
    icon: "solar:hard-hat-linear",
    links: [
      {
        label: t("navigation.overview"),
        icon: "solar:pie-chart-2-linear",
        children: [
          {
            label: t("navigation.dashboard"),
            to: "/workforce-hub",
            icon: "solar:speedometer-medium-linear",
            exact: true,
          },
        ],
      },
      {
        label: t("navigation.workforce_management"),
        icon: "solar:users-group-rounded-linear",
        children: [
          {
            label: t("navigation.workers"),
            to: "/workforce-hub/workers",
            icon: "solar:user-hard-hat-linear",
          },
          {
            label: t("navigation.job_requests"),
            to: "/workforce-hub/job-requests",
            icon: "solar:case-linear",
          },
          {
            label: t("navigation.matches"),
            to: "/workforce-hub/matches",
            icon: "solar:hand-shake-linear",
          },
          {
            label: t("navigation.assignments"),
            to: "/workforce-hub/assignments",
            icon: "solar:clipboard-text-linear",
          },
        ],
      },
      {
        label: t("navigation.rental_management"),
        icon: "solar:buildings-linear",
        children: [
          {
            label: t("navigation.rental_companies"),
            to: "/workforce-hub/rental-companies",
            icon: "solar:buildings-linear",
          },
        ],
      },
    ],
  },
  {
    label: t("navigation.budget"),
    icon: "solar:banknote-linear",
    links: [
      {
        label: t("navigation.overview"),
        icon: "solar:pie-chart-2-linear",
        children: [
          {
            label: t("navigation.dashboard"),
            to: "/budget",
            icon: "solar:pie-chart-2-linear",
            exact: true,
          },
          {
            label: t("navigation.quick_budget"),
            to: "/budget/quick-budget",
            icon: "solar:bolt-linear",
          },
          {
            label: t("navigation.budget_planner"),
            to: "/budget/planner",
            icon: "solar:calendar-linear",
          },
        ],
      },
      {
        label: t("navigation.transactions"),
        icon: "solar:card-linear",
        children: [
          {
            label: t("navigation.all_transactions"),
            to: "/budget/transactions",
            icon: "solar:receipt-linear",
          },
          {
            label: t("navigation.income"),
            to: "/budget/income",
            icon: "solar:arrow-down-left-linear",
          },
          {
            label: t("navigation.expenses"),
            to: "/budget/expenses",
            icon: "solar:arrow-up-right-linear",
          },
        ],
      },
      {
        label: t("navigation.assets_liabilities"),
        icon: "solar:home-linear",
        children: [
          {
            label: t("navigation.asset_overview"),
            to: "/budget/assets",
            icon: "solar:bank-linear",
          },
          {
            label: t("navigation.net_worth"),
            to: "/budget/net-worth",
            icon: "solar:trending-up-linear",
          },
          {
            label: t("navigation.debt_management"),
            to: "/budget/debt",
            icon: "solar:card-linear",
          },
        ],
      },
      {
        label: t("navigation.planning"),
        icon: "solar:calendar-linear",
        children: [
          {
            label: t("navigation.financial_goals"),
            to: "/budget/goals",
            icon: "solar:target-linear",
          },
          {
            label: t("navigation.budget_categories"),
            to: "/budget/categories",
            icon: "solar:folder-with-files-linear",
          },
        ],
      },
      {
        label: t("navigation.analytics"),
        icon: "solar:document-text-linear",
        children: [
          {
            label: t("navigation.financial_reports"),
            to: "/budget/reports",
            icon: "solar:document-text-linear",
          },
          {
            label: t("navigation.insights_analytics"),
            to: "/budget/insights",
            icon: "solar:chart-line-linear",
          },
          {
            label: t("navigation.forecasting"),
            to: "/budget/forecasting",
            icon: "solar:trending-up-linear",
          },
        ],
      },
      {
        label: t("navigation.settings"),
        icon: "solar:settings-linear",
        children: [
          {
            label: t("navigation.budget_settings"),
            to: "/budget/settings",
            icon: "solar:settings-linear",
          },
          {
            label: t("navigation.payment_methods"),
            to: "/budget/settings/payment-methods",
            icon: "solar:card-linear",
          },
          {
            label: t("navigation.notifications"),
            to: "/budget/settings/notifications",
            icon: "solar:bell-linear",
          },
          {
            label: t("navigation.data_import_export"),
            to: "/budget/settings/import-export",
            icon: "solar:file-download-linear",
          },
        ],
      },
    ],
  },
  {
    label: t("navigation.human_resources"),
    icon: "solar:users-group-two-rounded-linear",
    links: [
      {
        label: t("navigation.overview"),
        icon: "solar:pie-chart-2-linear",
        children: [
          {
            label: t("navigation.dashboard"),
            to: "/hr",
            icon: "solar:chart-line-up-linear",
            exact: true,
          },
          {
            label: t("navigation.analytics"),
            to: "/hr/analytics",
            icon: "solar:pie-chart-2-linear",
          },
        ],
      },
      {
        label: t("navigation.workforce"),
        icon: "solar:users-group-rounded-linear",
        children: [
          {
            label: t("navigation.employees"),
            to: "/hr/employees",
            icon: "solar:users-group-rounded-linear",
            children: [
              {
                label: t("navigation.all_employees"),
                to: "/hr/employees",
                exact: true,
              },
              {
                label: t("navigation.add_employee"),
                to: "/hr/employees/new",
              },
            ],
          },
          {
            label: t("navigation.departments"),
            to: "/hr/departments",
            icon: "solar:structure-linear",
          },
          {
            label: t("navigation.positions"),
            to: "/hr/positions",
            icon: "solar:case-linear",
          },
        ],
      },
      {
        label: t("navigation.time_management_hr"),
        icon: "solar:clock-circle-broken",
        children: [
          {
            label: t("navigation.leave_management"),
            to: "/hr/leave",
            icon: "solar:calendar-linear",
          },
          {
            label: t("navigation.attendance"),
            to: "/hr/attendance",
            icon: "solar:clock-linear",
          },
          {
            label: t("navigation.shifts"),
            to: "/hr/shifts",
            icon: "solar:clock-circle-linear",
          },
        ],
      },
      {
        label: t("navigation.compensation"),
        icon: "solar:banknote-linear",
        children: [
          {
            label: t("navigation.payroll"),
            to: "/hr/payroll",
            icon: "solar:banknote-linear",
          },
          {
            label: t("navigation.benefits"),
            to: "/hr/benefits",
            icon: "solar:heart-linear",
          },
          {
            label: t("navigation.bonuses"),
            to: "/hr/bonuses",
            icon: "solar:star-linear",
          },
        ],
      },
      {
        label: t("navigation.development"),
        icon: "solar:devices-line-duotone",
        children: [
          {
            label: t("navigation.performance"),
            to: "/hr/performance",
            icon: "solar:chart-square-linear",
          },
          {
            label: t("navigation.training"),
            to: "/hr/training",
            icon: "solar:graduation-cap-linear",
          },
          {
            label: t("navigation.career_paths"),
            to: "/hr/career-paths",
            icon: "solar:route-linear",
          },
        ],
      },
      {
        label: t("navigation.administration"),
        icon: "solar:settings-linear",
        children: [
          {
            label: t("navigation.documents"),
            to: "/hr/documents",
            icon: "solar:documents-linear",
          },
          {
            label: t("navigation.hr_settings"),
            to: "/hr/settings",
            icon: "solar:settings-linear",
          },
        ],
      },
    ],
  },
  {
    label: t("navigation.companies"),
    icon: "solar:buildings-linear",
    links: [
      {
        label: t("navigation.overview"),
        icon: "solar:pie-chart-2-linear",
        children: [
          {
            label: t("navigation.dashboard"),
            to: "/companies",
            icon: "solar:chart-line-up-linear",
            exact: true,
          },
          {
            label: t("navigation.analytics"),
            to: "/companies/analytics",
            icon: "solar:pie-chart-2-linear",
          },
        ],
      },
      {
        label: t("navigation.partners"),
        icon: "solar:hand-shake-linear",
        children: [
          {
            label: t("navigation.clients"),
            to: "/companies/clients",
            icon: "solar:hand-shake-linear",
          },
          {
            label: t("navigation.client_management"),
            to: "/companies/client-management",
            icon: "solar:users-group-rounded-linear",
          },
          {
            label: t("navigation.suppliers"),
            to: "/companies/suppliers",
            icon: "solar:truck-linear",
          },
          {
            label: t("navigation.supplier_management"),
            to: "/companies/supplier-management",
            icon: "solar:factory-linear",
          },
          {
            label: t("navigation.relationship_dashboard"),
            to: "/companies/relationships",
            icon: "solar:network-linear",
          },
        ],
      },
      {
        label: t("navigation.workforce_companies"),
        icon: "solar:users-group-two-rounded-linear",
        children: [
          {
            label: t("navigation.needs_assessment"),
            to: "/companies/workforce-needs",
            icon: "solar:users-group-two-rounded-linear",
          },
          {
            label: t("navigation.requests"),
            to: "/companies/requests",
            icon: "solar:pen-new-square-linear",
          },
        ],
      },
      {
        label: t("navigation.risk_management"),
        icon: "solar:danger-triangle-linear",
        children: [
          {
            label: t("navigation.risk_assessment"),
            to: "/companies/risk",
            icon: "solar:danger-triangle-linear",
          },
          {
            label: t("navigation.compliance"),
            to: "/companies/compliance",
            icon: "solar:check-circle-linear",
          },
        ],
      },
      {
        label: t("navigation.administration"),
        icon: "solar:settings-linear",
        children: [
          {
            label: t("navigation.company_profile"),
            to: "/companies/details",
            icon: "solar:buildings-linear",
          },
          {
            label: t("navigation.documents"),
            to: "/companies/documents",
            icon: "solar:documents-linear",
          },
          {
            label: t("navigation.team_members"),
            to: "/companies/team",
            icon: "solar:users-group-two-rounded-linear",
          },
          {
            label: t("navigation.user_transitions"),
            to: "/companies/transitions",
            icon: "solar:arrows-horizontal-linear",
          },
          {
            label: t("navigation.settings"),
            to: "/companies/settings",
            icon: "solar:settings-linear",
          },
        ],
      },
    ],
  },
  {
    label: t("navigation.production"),
    icon: "solar:buildings-2-outline",
    links: [
      {
        label: t("navigation.overview"),
        icon: "solar:pie-chart-2-linear",
        children: [
          {
            label: t("navigation.dashboard"),
            to: "/production",
            icon: "solar:chart-line-up-linear",
            exact: true,
          },
          {
            label: t("navigation.statistics"),
            to: "/production/statistics",
            icon: "solar:chart-square-linear",
          },
          {
            label: t("navigation.ai_insights"),
            to: "/production/ai-insights",
            icon: "solar:brain-linear",
          },
        ],
      },
      {
        label: t("navigation.projects"),
        icon: "solar:folder-with-files-linear",
        children: [
          {
            label: t("navigation.active_projects"),
            to: "/production/projects/active",
            icon: "solar:folder-open-linear",
          },
          {
            label: t("navigation.project_documents"),
            to: "/production/projects/documents",
            icon: "solar:documents-linear",
          },
          {
            label: t("navigation.project_timeline"),
            to: "/production/projects/timeline",
            icon: "solar:calendar-linear",
          },
        ],
      },
      {
        label: t("navigation.tasks"),
        icon: "solar:clipboard-check-linear",
        children: [
          {
            label: t("navigation.task_board"),
            to: "/production/tasks",
            icon: "solar:widget-4-linear",
          },
          {
            label: t("navigation.task_calendar"),
            to: "/production/tasks/calendar",
            icon: "solar:calendar-check-linear",
          },
          {
            label: t("navigation.assignments"),
            to: "/production/tasks/assignments",
            icon: "solar:users-group-rounded-linear",
          },
        ],
      },
      {
        label: t("navigation.quality_control"),
        icon: "solar:pen-new-square-linear",
        children: [
          {
            label: t("navigation.qc_dashboard"),
            to: "/production/quality",
            icon: "solar:square-check-linear",
          },
          {
            label: t("navigation.checklists"),
            to: "/production/quality/checklists",
            icon: "solar:list-check-linear",
          },
          {
            label: t("navigation.photo_evidence"),
            to: "/production/quality/photos",
            icon: "solar:camera-linear",
          },
        ],
      },
      {
        label: t("navigation.resources"),
        icon: "solar:box-linear",
        children: [
          {
            label: t("navigation.equipment"),
            to: "/production/resources/equipment",
            icon: "solar:settings-linear",
          },
          {
            label: t("navigation.materials"),
            to: "/production/resources/materials",
            icon: "solar:box-linear",
          },
          {
            label: t("navigation.workforce_production"),
            to: "/production/resources/workforce",
            icon: "solar:users-group-two-rounded-linear",
          },
        ],
      },
      {
        label: t("navigation.reports"),
        icon: "solar:document-text-linear",
        children: [
          {
            label: t("navigation.production_reports"),
            to: "/production/reports",
            icon: "solar:chart-line-up-linear",
          },
          {
            label: t("navigation.quality_metrics"),
            to: "/production/reports/quality",
            icon: "solar:pie-chart-2-linear",
          },
          {
            label: t("navigation.efficiency_analysis"),
            to: "/production/reports/efficiency",
            icon: "solar:chart-square-linear",
          },
        ],
      },
    ],
  },
  {
    label: t("navigation.accounting"),
    icon: "solar:calculator-linear",
    links: [
      {
        label: t("navigation.overview"),
        icon: "solar:pie-chart-2-linear",
        children: [
          {
            label: t("navigation.dashboard"),
            to: "/accounting",
            icon: "solar:chart-line-up-linear",
            exact: true,
          },
          {
            label: t("navigation.financial_overview"),
            to: "/accounting/overview",
            icon: "solar:banknote-linear",
          },
        ],
      },
      {
        label: t("navigation.transactions"),
        icon: "solar:card-linear",
        children: [
          {
            label: t("navigation.journal_entries"),
            to: "/accounting/journal",
            icon: "solar:book-open-linear",
          },
          {
            label: t("navigation.reconciliation"),
            to: "/accounting/reconciliation",
            icon: "solar:square-check-linear",
          },
          {
            label: t("navigation.transaction_history"),
            to: "/accounting/transactions",
            icon: "solar:clock-square-linear",
          },
        ],
      },
      {
        label: t("navigation.accounts"),
        icon: "solar:folder-with-files-linear",
        children: [
          {
            label: t("navigation.chart_of_accounts"),
            to: "/accounting/accounts",
            icon: "solar:structure-linear",
          },
          {
            label: t("navigation.asset_accounts"),
            to: "/accounting/accounts/assets",
            icon: "solar:buildings-linear",
          },
          {
            label: t("navigation.liability_accounts"),
            to: "/accounting/accounts/liabilities",
            icon: "solar:card-linear",
          },
          {
            label: t("navigation.equity_accounts"),
            to: "/accounting/accounts/equity",
            icon: "solar:bank-linear",
          },
        ],
      },
      {
        label: t("navigation.reports"),
        icon: "solar:document-text-linear",
        children: [
          {
            label: t("navigation.financial_reports"),
            to: "/accounting/reports",
            icon: "solar:document-text-linear",
          },
          {
            label: t("navigation.balance_sheet"),
            to: "/accounting/reports/balance-sheet",
            icon: "solar:scale-linear",
          },
          {
            label: t("navigation.income_statement"),
            to: "/accounting/reports/income",
            icon: "solar:trending-up-linear",
          },
          {
            label: t("navigation.cash_flow"),
            to: "/accounting/reports/cash-flow",
            icon: "solar:arrows-horizontal-linear",
          },
        ],
      },
      {
        label: t("navigation.settings"),
        icon: "solar:settings-linear",
        children: [
          {
            label: t("navigation.account_settings"),
            to: "/accounting/settings",
            icon: "solar:settings-linear",
          },
          {
            label: t("navigation.tax_configuration"),
            to: "/accounting/settings/tax",
            icon: "solar:percent-linear",
          },
          {
            label: t("navigation.fiscal_periods"),
            to: "/accounting/settings/fiscal-periods",
            icon: "solar:calendar-linear",
          },
        ],
      },
    ],
  },
  {
    label: t("navigation.recruitment"),
    icon: "solar:users-group-rounded-linear",
    links: [
      {
        label: t("navigation.overview"),
        icon: "solar:pie-chart-2-linear",
        children: [
          {
            label: t("navigation.dashboard"),
            to: "/recruitment",
            icon: "solar:speedometer-medium-linear",
            exact: true,
          },
          {
            label: t("navigation.analytics"),
            to: "/recruitment/analytics",
            icon: "solar:chart-line-linear",
          },
        ],
      },
      {
        label: t("navigation.applications"),
        icon: "solar:clipboard-text-linear",
        children: [
          {
            label: t("navigation.all_applications"),
            to: "/recruitment/applications",
            icon: "solar:clipboard-text-linear",
          },
          {
            label: t("navigation.pending_review"),
            to: "/recruitment/applications/pending",
            icon: "solar:hourglass-linear",
          },
          {
            label: t("navigation.shortlisted"),
            to: "/recruitment/applications/shortlisted",
            icon: "solar:star-linear",
          },
          {
            label: t("navigation.interviews"),
            to: "/recruitment/applications/interviews",
            icon: "solar:users-group-rounded-linear",
          },
          {
            label: t("navigation.join_requests"),
            to: "/recruitment/company-requests",
            icon: "solar:users-group-two-rounded-linear",
          },
        ],
      },
      {
        label: t("navigation.job_postings"),
        icon: "solar:case-linear",
        children: [
          {
            label: t("navigation.active_positions"),
            to: "/recruitment/jobs/active",
            icon: "solar:case-linear",
          },
          {
            label: t("navigation.draft_positions"),
            to: "/recruitment/jobs/drafts",
            icon: "solar:document-text-linear",
          },
          {
            label: t("navigation.closed_positions"),
            to: "/recruitment/jobs/closed",
            icon: "solar:box-minimalistic-linear",
          },
        ],
      },

      {
        label: t("navigation.form_builder"),
        icon: "solar:clipboard-text-linear",
        children: [
          {
            label: t("navigation.all_forms"),
            to: "/recruitment/forms",
            icon: "solar:clipboard-text-linear",
          },
          {
            label: t("navigation.create_form"),
            to: "/recruitment/forms/create",
            icon: "solar:add-circle-linear",
          },
        ],
      },
      {
        label: t("navigation.settings"),
        icon: "solar:settings-linear",
        children: [
          {
            label: t("navigation.form_settings"),
            to: "/recruitment/form-settings",
            icon: "solar:slider-horizontal-linear",
          },
          {
            label: t("navigation.workflow"),
            to: "/recruitment/settings/workflow",
            icon: "solar:arrow-right-linear",
          },
          {
            label: t("navigation.email_templates"),
            to: "/recruitment/settings/templates",
            icon: "solar:letter-linear",
          },
          {
            label: t("navigation.assessment_forms"),
            to: "/recruitment/settings/assessments",
            icon: "solar:notebook-linear",
          },
        ],
      },
      {
        label: t("navigation.reports"),
        icon: "solar:document-text-linear",
        children: [
          {
            label: t("navigation.recruitment_metrics"),
            to: "/recruitment/reports/metrics",
            icon: "solar:chart-square-linear",
          },
          {
            label: t("navigation.source_analysis"),
            to: "/recruitment/reports/sources",
            icon: "solar:funnel-linear",
          },
          {
            label: t("navigation.time_to_hire"),
            to: "/recruitment/reports/time-to-hire",
            icon: "solar:timer-linear",
          },
        ],
      },
    ],
  },
  {
    label: t("navigation.sales"),
    icon: "solar:box-linear",
    links: [
      {
        label: t("navigation.overview"),
        icon: "solar:pie-chart-2-linear",
        children: [
          {
            label: t("navigation.dashboard"),
            to: "/sales",
            icon: "solar:chart-line-up-linear",
            exact: true,
          },
          {
            label: t("navigation.pipeline"),
            to: "/sales/pipeline",
            icon: "solar:columns-linear",
          },
        ],
      },
      {
        label: t("navigation.leads"),
        icon: "solar:user-plus-rounded-linear",
        children: [
          {
            label: t("navigation.all_leads"),
            to: "/sales/leads",
            icon: "solar:funnel-linear",
          },
          {
            label: t("navigation.new_lead"),
            to: "/sales/leads/new",
            icon: "solar:user-plus-rounded-linear",
          },
          {
            label: t("navigation.import_leads"),
            to: "/sales/leads/import",
            icon: "solar:file-upload-linear",
          },
        ],
      },
      {
        label: t("navigation.deals"),
        icon: "solar:hand-shake-linear",
        children: [
          {
            label: t("navigation.all_deals"),
            to: "/sales/deals",
            icon: "solar:hand-shake-linear",
          },
          {
            label: t("navigation.new_deal"),
            to: "/sales/deals/new",
            icon: "solar:add-circle-linear",
          },
        ],
      },
      {
        label: t("navigation.activities"),
        icon: "solar:calendar-linear",
        children: [
          {
            label: t("navigation.calendar"),
            to: "/sales/calendar",
            icon: "solar:calendar-linear",
          },
          {
            label: t("navigation.all_activities"),
            to: "/sales/activities",
            icon: "solar:calendar-check-linear",
          },
        ],
      },
      {
        label: t("navigation.marketing"),
        icon: "solar:letter-linear",
        children: [
          {
            label: t("navigation.campaigns"),
            to: "/sales/campaigns",
            icon: "solar:megaphone-linear",
          },
          {
            label: t("navigation.email_templates_sales"),
            to: "/sales/campaigns/templates",
            icon: "solar:letter-linear",
          },
        ],
      },
      {
        label: t("navigation.products_quotes"),
        icon: "solar:box-linear",
        children: [
          {
            label: t("navigation.products"),
            to: "/sales/products",
            icon: "solar:box-linear",
          },
          {
            label: t("navigation.quotations"),
            to: "/sales/quotations",
            icon: "solar:document-text-linear",
          },
          {
            label: t("navigation.quote_templates"),
            to: "/sales/quotations/templates",
            icon: "solar:file-add-linear",
          },
        ],
      },
      {
        label: t("navigation.sales_analytics"),
        icon: "solar:chart-square-linear",
        children: [
          {
            label: t("navigation.sales_performance"),
            to: "/sales/analytics/performance",
            icon: "solar:chart-square-linear",
          },
          {
            label: t("navigation.forecasts"),
            to: "/sales/analytics/forecasts",
            icon: "solar:trending-up-linear",
          },
          {
            label: t("navigation.sales_reports"),
            to: "/sales/analytics/reports",
            icon: "solar:document-text-linear",
          },
        ],
      },
      {
        label: t("navigation.settings"),
        icon: "solar:settings-linear",
        children: [
          {
            label: t("navigation.pipeline_stages"),
            to: "/sales/settings/stages",
            icon: "solar:widget-4-linear",
          },
          {
            label: t("navigation.sales_goals"),
            to: "/sales/settings/goals",
            icon: "solar:target-linear",
          },
          {
            label: t("navigation.automation"),
            to: "/sales/settings/automation",
            icon: "solar:magic-stick-linear",
          },
          {
            label: t("navigation.api_keys"),
            to: "/sales/settings/api-keys",
            icon: "solar:key-linear",
          },
          {
            label: t("navigation.integrations"),
            to: "/sales/settings/integrations",
            icon: "solar:plug-circle-linear",
          },
        ],
      },
    ],
  },
  {
    label: t("navigation.time_management"),
    icon: "solar:clock-circle-linear",
    links: [
      {
        label: t("navigation.overview"),
        icon: "solar:pie-chart-2-linear",
        children: [
          {
            label: t("navigation.dashboard"),
            to: "/timemanagement",
            icon: "solar:chart-line-up-linear",
            exact: true,
          },
          {
            label: t("navigation.analytics"),
            to: "/timemanagement/analytics",
            icon: "solar:pie-chart-2-linear",
          },
        ],
      },
      {
        label: t("navigation.time_tracking"),
        icon: "solar:clock-circle-linear",
        children: [
          {
            label: t("navigation.active_sessions"),
            to: "/timemanagement/active",
            icon: "solar:play-circle-linear",
          },
          {
            label: t("navigation.time_entries"),
            to: "/timemanagement/entries",
            icon: "solar:clock-linear",
          },
          {
            label: t("navigation.calendar_view"),
            to: "/timemanagement/calendar",
            icon: "solar:calendar-linear",
          },
          {
            label: t("navigation.location_tracking"),
            to: "/timemanagement/locations",
            icon: "solar:map-point-linear",
          },
        ],
      },
      {
        label: t("navigation.workers"),
        icon: "solar:users-group-rounded-linear",
        children: [
          {
            label: t("navigation.worker_dashboard"),
            to: "/timemanagement/worker",
            icon: "solar:user-linear",
          },
          {
            label: t("navigation.team_overview"),
            to: "/timemanagement/team",
            icon: "solar:users-group-two-rounded-linear",
          },
          {
            label: t("navigation.assignments"),
            to: "/timemanagement/assignments",
            icon: "solar:clipboard-text-linear",
          },
        ],
      },
      {
        label: t("navigation.reports"),
        icon: "solar:document-text-linear",
        children: [
          {
            label: t("navigation.time_reports"),
            to: "/timemanagement/reports",
            icon: "solar:document-text-linear",
          },
          {
            label: t("navigation.timesheets"),
            to: "/timemanagement/timesheets",
            icon: "solar:table-linear",
          },
          {
            label: t("navigation.payroll_export"),
            to: "/timemanagement/payroll",
            icon: "solar:banknote-linear",
          },
          {
            label: t("navigation.client_reports"),
            to: "/timemanagement/client-reports",
            icon: "solar:buildings-linear",
          },
        ],
      },
      {
        label: t("navigation.settings"),
        icon: "solar:settings-linear",
        children: [
          {
            label: t("navigation.general_settings"),
            to: "/timemanagement/settings",
            icon: "solar:settings-linear",
          },
          {
            label: t("navigation.work_locations"),
            to: "/timemanagement/settings/locations",
            icon: "solar:map-linear",
          },
          {
            label: t("navigation.approval_workflows"),
            to: "/timemanagement/settings/approvals",
            icon: "solar:arrow-right-linear",
          },
          {
            label: t("navigation.integrations"),
            to: "/timemanagement/settings/integrations",
            icon: "solar:plug-circle-linear",
          },
        ],
      },
    ],
  },
  {
    label: t("navigation.communication"),
    icon: "solar:chat-round-unread-linear",
    badge: () => {
      return communicationStore.totalUnreadCount > 0
        ? communicationStore.totalUnreadCount
        : undefined;
    },
    links: [
      {
        label: t("navigation.overview"),
        icon: "solar:pie-chart-2-linear",
        children: [
          {
            label: t("navigation.dashboard"),
            to: "/communication",
            icon: "solar:layout-linear",
            exact: true,
            badge: () => {
              return communicationStore.totalUnreadCount > 0
                ? communicationStore.totalUnreadCount
                : undefined;
            },
          },
        ],
      },
      {
        label: t("navigation.email"),
        to: "/communication/email",
        icon: "solar:letter-unread-linear",
        badge: () => {
          return communicationStore.unreadEmailCount > 0
            ? communicationStore.unreadEmailCount
            : undefined;
        },
      },
      {
        label: t("navigation.chat"),
        to: "/communication/chat",
        icon: "solar:chat-round-unread-linear",
        badge: () => {
          return communicationStore.unreadChatCount > 0
            ? communicationStore.unreadChatCount
            : undefined;
        },
      },
      {
        label: t("navigation.api_endpoints"),
        icon: "solar:add-circle-linear",
        children: [
          {
            label: t("navigation.manage_endpoints"),
            to: "/communication/endpoints",
            icon: "solar:plug-linear",
          },
          {
            label: t("navigation.create_endpoint"),
            to: "/communication/endpoints/create",
            icon: "solar:add-circle-linear",
          },
        ],
      },
    ],
  },
]);

// Setup variables
const isSearchOpen = useSearchOpen();

const route = useRoute();

// Helper function to find active module (shared logic)
function findActiveModule() {
  // Search for the active menu item based on route
  for (const item of menu.value) {
    // Check if item has direct 'to' property (non-collapsible items)
    if ("to" in item && item.to === route.path) {
      return item.label;
    }

    // Check if item has 'links' property (collapsible items)
    if (
      "links" in item &&
      item.links.some(
        (link) =>
          ("to" in link &&
            (link.to === route.path || route.path.startsWith(link.to + "/"))) ||
          (link.children &&
            link.children.some(
              (child) =>
                child.to === route.path || route.path.startsWith(child.to + "/")
            ))
      )
    ) {
      return item.label;
    }
  }
  return "Dashboards";
}

// Separate computed for active module (for highlighting main navigation)
const activeModuleId = computed(() => findActiveModule());

function getRouteSidebarId() {
  if (route.path.startsWith("/dashboards/inbox")) {
    return "Inbox";
  }
  if (route.path.startsWith("/calendar")) {
    return "Calendar";
  }
  if (route.path.startsWith("/map")) {
    return "Map";
  }

  // For all other routes, use the active module as the sidebar
  return findActiveModule();
}

const sidebarId = ref(getRouteSidebarId());

watch(
  () => route.path,
  () => {
    sidebarId.value = getRouteSidebarId();
  }
);
</script>

<template>
  <SidebarLayout
    v-slot="{ toggleMobileNav }"
    v-model="sidebarId"
    :class="[
      sidebarId === 'Inbox' ? '[--tairo-sidebar-subsidebar-width:3.5rem]' : '',
      sidebarId === 'Calendar'
        ? '[--tairo-sidebar-subsidebar-width:3.5rem]'
        : '',
      sidebarId === 'Map' ? '[--tairo-sidebar-subsidebar-width:0rem]' : '',
      sidebarId === t('navigation.communication') &&
      route.path.startsWith('/communication/email')
        ? '[--tairo-sidebar-subsidebar-width:3.5rem]'
        : '',
      sidebarId === t('navigation.communication') &&
      route.path.startsWith('/communication/chat')
        ? '[--tairo-sidebar-subsidebar-width:4.5rem]'
        : '',
    ]"
  >
    <SidebarNav>
      <Sidebar>
        <NuxtLink
          to="/"
          class="flex items-center justify-center size-14 shrink-0"
        >
          <Logo class="size-8 text-primary-heavy dark:text-primary-light" />
        </NuxtLink>

        <SidebarLinks class="overflow-y-auto nui-slimscroll">
          <BaseTooltip
            v-for="item in menu"
            :key="item.label"
            :content="item.label"
            variant="dark"
            :bindings="{
              content: { side: 'left' },
              portal: { disabled: true },
            }"
          >
            <SidebarTrigger
              :value="item.label"
              :to="'to' in item ? item.to : undefined"
            >
              <Icon :name="item.icon" class="size-5" />
            </SidebarTrigger>
          </BaseTooltip>

          <BaseTooltip
            :content="t('ui.calendar')"
            variant="dark"
            :bindings="{
              content: { side: 'left' },
              portal: { disabled: true },
            }"
          >
            <SidebarTrigger value="Calendar" to="/calendar">
              <Icon name="solar:calendar-linear" class="size-5" />
            </SidebarTrigger>
          </BaseTooltip>
          <BaseTooltip
            :content="t('ui.map')"
            variant="dark"
            :bindings="{
              content: { side: 'left' },
              portal: { disabled: true },
            }"
          >
            <SidebarTrigger value="Map" to="/map">
              <Icon name="solar:map-point-wave-linear" class="size-5" />
            </SidebarTrigger>
          </BaseTooltip>
        </SidebarLinks>

        <SidebarLinks class="shrink-0 mt-auto">
          <!-- Color switcher removed -->
          <BaseTooltip
            :content="t('ui.settings')"
            variant="dark"
            :bindings="{
              content: { side: 'left' },
              portal: { disabled: true },
            }"
          >
            <SidebarLink to="/layouts/preferences">
              <Icon name="solar:settings-linear" class="size-5" />
            </SidebarLink>
          </BaseTooltip>
          <!-- Theme toggle moved to Toolbar -->
          <SidebarLink to="/layouts/profile">
            <BaseChip
              size="sm"
              pulse
              color="custom"
              :offset="3"
              class="text-green-600 flex items-center justify-center"
            >
              <BaseAvatar size="xs" src="/img/avatars/10.svg" />
            </BaseChip>
          </SidebarLink>
        </SidebarLinks>
      </Sidebar>

      <SidebarSubsidebar
        v-for="item in menu"
        :key="item.label"
        :value="item.label"
        :class="[
          item.label === t('navigation.communication') &&
          route.path.startsWith('/communication/email')
            ? 'flex flex-col items-center'
            : '',
        ]"
      >
        <!-- Special layout for Communication module when on email pages -->
        <template
          v-if="
            item.label === t('navigation.communication') &&
            route.path.startsWith('/communication/email')
          "
        >
          <SidebarSubsidebarContent>
            <div class="flex h-12 w-full items-center justify-center shrink-0">
              <BaseTooltip
                :content="t('navigation.compose')"
                variant="dark"
                :bindings="{
                  content: { side: 'left' },
                  portal: { disabled: true },
                }"
              >
                <BaseButton
                  size="icon-sm"
                  rounded="full"
                  variant="primary"
                  @click="navigateTo('/communication/email/compose')"
                >
                  <Icon name="lucide:plus" class="size-4" />
                </BaseButton>
              </BaseTooltip>
            </div>
            <div class="flex h-12 w-full items-center justify-center shrink-0">
              <BaseTooltip
                :content="t('ui.received')"
                variant="dark"
                :bindings="{
                  content: { side: 'left' },
                  portal: { disabled: true },
                }"
              >
                <BaseButton
                  size="icon-sm"
                  rounded="md"
                  :variant="
                    route.path === '/communication/email' ? 'primary' : 'ghost'
                  "
                  @click="navigateTo('/communication/email')"
                >
                  <Icon name="solar:inbox-linear" class="size-5" />
                </BaseButton>
              </BaseTooltip>
            </div>
            <div class="flex h-12 w-full items-center justify-center shrink-0">
              <BaseTooltip
                :content="t('ui.sent')"
                variant="dark"
                :bindings="{
                  content: { side: 'left' },
                  portal: { disabled: true },
                }"
              >
                <BaseButton
                  size="icon-sm"
                  rounded="md"
                  :variant="
                    route.path === '/communication/email/sent'
                      ? 'primary'
                      : 'ghost'
                  "
                  @click="navigateTo('/communication/email/sent')"
                >
                  <Icon name="solar:inbox-out-linear" class="size-5" />
                </BaseButton>
              </BaseTooltip>
            </div>
            <div class="flex h-12 w-full items-center justify-center shrink-0">
              <BaseTooltip
                :content="t('ui.important')"
                variant="dark"
                :bindings="{
                  content: { side: 'left' },
                  portal: { disabled: true },
                }"
              >
                <BaseButton
                  size="icon-sm"
                  rounded="md"
                  :variant="
                    route.path === '/communication/email/important'
                      ? 'primary'
                      : 'ghost'
                  "
                  @click="navigateTo('/communication/email/important')"
                >
                  <Icon name="solar:bookmark-linear" class="size-5" />
                </BaseButton>
              </BaseTooltip>
            </div>
            <div class="flex h-12 w-full items-center justify-center shrink-0">
              <BaseTooltip
                :content="t('ui.spam')"
                variant="dark"
                :bindings="{
                  content: { side: 'left' },
                  portal: { disabled: true },
                }"
              >
                <BaseButton
                  size="icon-sm"
                  rounded="md"
                  :variant="
                    route.path === '/communication/email/spam'
                      ? 'primary'
                      : 'ghost'
                  "
                  @click="navigateTo('/communication/email/spam')"
                >
                  <Icon name="solar:trash-bin-trash-linear" class="size-5" />
                </BaseButton>
              </BaseTooltip>
            </div>
          </SidebarSubsidebarContent>
        </template>

        <!-- Special layout for Communication module when on chat pages -->
        <template
          v-else-if="
            item.label === t('navigation.communication') &&
            route.path.startsWith('/communication/chat')
          "
        >
          <SubsidebarChat />
        </template>

        <!-- Normal layout for all other cases -->
        <template v-else>
          <SidebarSubsidebarHeader>
            {{ item.label }}
          </SidebarSubsidebarHeader>
          <SidebarSubsidebarContent>
            <!-- Search bar moved from Toolbar -->
            <div
              role="button"
              class="cursor-pointer h-8 mb-4 w-full flex items-center justify-between bg-white dark:bg-muted-950 text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:ring-muted-300 dark:hover:ring-muted-700 gap-2 ps-3 pe-1 py-1 rounded-md ring-1 ring-muted-200 dark:ring-muted-800 transition-colors duration-300"
              @click="isSearchOpen = true"
            >
              <div class="pointer-events-none">
                <span class="font-sans text-sm">
                  {{ t("components.toolbar.search") }}
                </span>
              </div>
              <div class="flex gap-1">
                <BaseKbd
                  size="sm"
                  variant="default"
                  class="!font-semibold h-6!"
                >
                  Ctrl
                </BaseKbd>
                <BaseKbd
                  size="sm"
                  variant="default"
                  class="!px-2 !font-semibold h-6!"
                >
                  K
                </BaseKbd>
              </div>
            </div>
            <template v-for="link in item.links" :key="link.label">
              <SidebarSubsidebarLink v-if="!link.children" :to="link.to">
                <Icon
                  :name="link.icon"
                  class="size-4 text-muted-500 dark:text-muted-400"
                />
                <span>{{ link.label }}</span>
              </SidebarSubsidebarLink>
              <SidebarSubsidebarCollapsible
                v-else
                :children="link.children"
                :default-open="
                  link.children.some((child) => child.to === $route.path) ||
                  undefined
                "
              >
                <template #trigger>
                  <SidebarSubsidebarCollapsibleTrigger>
                    <Icon
                      :name="link.icon"
                      class="size-4 text-muted-500 dark:text-muted-400"
                    />
                    <span>{{ link.label }}</span>
                  </SidebarSubsidebarCollapsibleTrigger>
                </template>
              </SidebarSubsidebarCollapsible>
            </template>
          </SidebarSubsidebarContent>
        </template>
      </SidebarSubsidebar>

      <SidebarSubsidebar value="Calendar" class="flex flex-col items-center">
        <SidebarSubsidebarContent>
          <div class="flex h-12 w-full items-center justify-center shrink-0">
            <BaseButton
              size="icon-sm"
              rounded="full"
              variant="primary"
              @click="handleCreateNewEvent"
            >
              <Icon name="lucide:plus" class="size-4" />
            </BaseButton>
          </div>

          <!-- Calendar Categories -->
          <CalendarSidebarCategories />
        </SidebarSubsidebarContent>
      </SidebarSubsidebar>
    </SidebarNav>

    <SidebarContent class="min-h-screen flex flex-col">
      <div
        class="px-4 md:px-6 xl:px-8 bg-white dark:bg-neutral-900 sticky top-0 z-10 shrink-0 border-b border-neutral-300 dark:border-neutral-700"
      >
        <Toolbar @toggle-mobile-nav="toggleMobileNav" />
      </div>
      <div class="flex-1 flex flex-col">
        <slot />
      </div>
    </SidebarContent>

    <!-- PWA Install Components -->
    <ClientOnly>
      <!-- Custom modal for PWA installation -->
      <PwaInstallModal
        :show-after-delay="true"
        :delay-in-seconds="30"
        :force-show-in-dev="true"
      />
    </ClientOnly>
  </SidebarLayout>
</template>
